using System;
using System.IO;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media.Animation;

namespace DesktopAssistantWidget
{
    public partial class MessageWindow : Window
    {
        private string messageFilePath;

        public MessageWindow()
        {
            InitializeComponent();
            InitializeMessageWindow();
        }

        private void InitializeMessageWindow()
        {
            // 設定訊息檔案路徑（與MainWindow一致）
            string today = DateTime.Now.ToString("yyyy-MM-dd");
            string chatLogsDir = "chat_logs";
            messageFilePath = Path.Combine(chatLogsDir, $"{today}.txt");

            // 載入今日訊息
            LoadTodayMessages();

            // 設定視窗位置（右下角）
            SetWindowPosition();

            // 設定鍵盤事件
            this.KeyDown += MessageWindow_KeyDown;

            // 設定滑鼠右鍵事件
            this.MouseRightButtonDown += MessageWindow_MouseRightButtonDown;

            // 設定淡入動畫
            this.Opacity = 0;
            var fadeInAnimation = new DoubleAnimation(0, 1, TimeSpan.FromMilliseconds(300));
            this.BeginAnimation(OpacityProperty, fadeInAnimation);

            // 移除焦點設定
        }

        private void SetWindowPosition()
        {
            // 設定視窗在右下角
            this.Left = SystemParameters.PrimaryScreenWidth - this.Width - 20;
            this.Top = SystemParameters.PrimaryScreenHeight - this.Height - 50;
        }

        private void LoadTodayMessages()
        {
            try
            {
                if (File.Exists(messageFilePath))
                {
                    string content = File.ReadAllText(messageFilePath);
                    MessageTextBlock.Text = content;
                }
                else
                {
                    // 建立新檔案
                    File.WriteAllText(messageFilePath, $"=== {DateTime.Now:yyyy-MM-dd} 訊息記錄 ==={Environment.NewLine}");
                    MessageTextBlock.Text = $"=== {DateTime.Now:yyyy-MM-dd} 訊息記錄 ===\n";
                }

                // 滾動到底部
                ScrollToEnd();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"載入訊息記錄時發生錯誤：{ex.Message}", "錯誤", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ScrollToEnd()
        {
            MessageScrollViewer.ScrollToEnd();
        }

        public void RefreshMessages()
        {
            LoadTodayMessages();
        }

        private void MessageWindow_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Escape)
            {
                HideWindow();
            }
        }

        private void MessageWindow_MouseRightButtonDown(object sender, MouseButtonEventArgs e)
        {
            // 右鍵點擊關閉視窗
            HideWindow();
            e.Handled = true;
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            HideWindow();
        }

        private void HideWindow()
        {
            var fadeOutAnimation = new DoubleAnimation(1, 0, TimeSpan.FromMilliseconds(300));
            fadeOutAnimation.Completed += (s, e) => this.Hide();
            this.BeginAnimation(OpacityProperty, fadeOutAnimation);
        }

        public void ShowWindow()
        {
            this.Show();
            var fadeInAnimation = new DoubleAnimation(0, 1, TimeSpan.FromMilliseconds(300));
            this.BeginAnimation(OpacityProperty, fadeInAnimation);
            RefreshMessages(); // 重新載入訊息
        }
    }
}
