<Window x:Class="DesktopAssistantWidget.IpApplicationWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="🌐 IP申請表單" Height="500" Width="400"
        WindowStyle="SingleBorderWindow"
        ResizeMode="NoResize"
        Topmost="True"
        ShowInTaskbar="False"
        Background="#F0F8FF">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 表單內容 -->
        <ScrollViewer Grid.Row="0" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- 單位 -->
                <Grid Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="單位 *" VerticalAlignment="Center" FontWeight="Bold"/>
                    <TextBox x:Name="UnitTextBox" Grid.Column="1" Height="30" Padding="5" FontSize="14"/>
                </Grid>
                
                <!-- IP -->
                <Grid Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="IP *" VerticalAlignment="Center" FontWeight="Bold"/>
                    <TextBox x:Name="IpTextBox" Grid.Column="1" Height="30" Padding="5" FontSize="14"/>
                </Grid>
                
                <!-- MAC -->
                <Grid Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="MAC *" VerticalAlignment="Center" FontWeight="Bold"/>
                    <TextBox x:Name="MacTextBox" Grid.Column="1" Height="30" Padding="5" FontSize="14"/>
                </Grid>
                
                <!-- 類型 -->
                <Grid Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="類型 *" VerticalAlignment="Center" FontWeight="Bold"/>
                    <ComboBox x:Name="TypeComboBox" Grid.Column="1" Height="30" FontSize="14" SelectedIndex="0">
                        <ComboBoxItem Content="OA電腦"/>
                        <ComboBoxItem Content="NBT"/>
                        <ComboBoxItem Content="筆記型電腦"/>
                        <ComboBoxItem Content="IOT設備"/>
                        <ComboBoxItem Content="補褶機"/>
                        <ComboBoxItem Content="ATM"/>
                    </ComboBox>
                </Grid>
                
                <!-- 申請人 -->
                <Grid Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="申請人 *" VerticalAlignment="Center" FontWeight="Bold"/>
                    <TextBox x:Name="ApplicantTextBox" Grid.Column="1" Height="30" Padding="5" FontSize="14"/>
                </Grid>
                
                <!-- 備註 -->
                <Grid Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="備註" VerticalAlignment="Top" FontWeight="Bold" Margin="0,5,0,0"/>
                    <TextBox x:Name="RemarksTextBox" Grid.Column="1" Height="60" Padding="5" FontSize="14" 
                             AcceptsReturn="True" TextWrapping="Wrap" VerticalScrollBarVisibility="Auto"/>
                </Grid>
                
                <!-- 說明文字 -->
                <TextBlock Text="* 為必填欄位" FontSize="12" Foreground="Red" Margin="0,10,0,0"/>
                <TextBlock Text="備註欄位如留空，將自動填入當日日期" FontSize="12" Foreground="Gray" Margin="0,5,0,0"/>
            </StackPanel>
        </ScrollViewer>
        
        <!-- 按鈕區域 -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="SubmitButton" Content="送出" Width="80" Height="35" Margin="5" 
                    Background="#4CAF50" Foreground="White" FontWeight="Bold" Click="SubmitButton_Click"/>
            <Button x:Name="ClearButton" Content="清除全部" Width="80" Height="35" Margin="5" 
                    Background="#FF9800" Foreground="White" FontWeight="Bold" Click="ClearButton_Click"/>
            <Button x:Name="CloseButton" Content="關閉" Width="80" Height="35" Margin="5" 
                    Background="#F44336" Foreground="White" FontWeight="Bold" Click="CloseButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
