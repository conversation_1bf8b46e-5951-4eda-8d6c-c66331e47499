# 修復圖片載入問題的 PowerShell 腳本

Write-Host "=== 修復圖片載入問題 ===" -ForegroundColor Green

# 1. 清理專案
Write-Host "1. 清理專案..." -ForegroundColor Yellow
dotnet clean

# 2. 創建輸出目錄
Write-Host "2. 創建輸出目錄..." -ForegroundColor Yellow
$outputDir = "bin\Debug\net6.0-windows\Assets"
if (!(Test-Path $outputDir)) {
    New-Item -ItemType Directory -Force -Path $outputDir
    Write-Host "   已創建目錄: $outputDir" -ForegroundColor Green
} else {
    Write-Host "   目錄已存在: $outputDir" -ForegroundColor Green
}

# 3. 複製圖片檔案
Write-Host "3. 複製圖片檔案..." -ForegroundColor Yellow
if (Test-Path "Assets") {
    $imageFiles = Get-ChildItem "Assets\*.png"
    foreach ($file in $imageFiles) {
        Copy-Item $file.FullName $outputDir
        Write-Host "   已複製: $($file.Name)" -ForegroundColor Green
    }
} else {
    Write-Host "   警告: Assets 資料夾不存在！" -ForegroundColor Red
}

# 4. 編譯專案
Write-Host "4. 編譯專案..." -ForegroundColor Yellow
dotnet build

# 5. 檢查編譯結果
if ($LASTEXITCODE -eq 0) {
    Write-Host "5. 編譯成功！" -ForegroundColor Green
    
    # 6. 列出複製的檔案
    Write-Host "6. 檢查複製的圖片檔案:" -ForegroundColor Yellow
    if (Test-Path $outputDir) {
        $copiedFiles = Get-ChildItem "$outputDir\*.png"
        foreach ($file in $copiedFiles) {
            Write-Host "   ✓ $($file.Name)" -ForegroundColor Green
        }
    }
    
    Write-Host "=== 修復完成！可以運行程式了 ===" -ForegroundColor Green
    Write-Host "執行命令: dotnet run" -ForegroundColor Cyan
} else {
    Write-Host "5. 編譯失敗！請檢查錯誤訊息。" -ForegroundColor Red
}

Write-Host "按任意鍵繼續..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
