<Window x:Class="DesktopAssistantWidget.MessageWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="訊息記錄" Height="400" Width="350"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        Topmost="True"
        ResizeMode="CanResize"
        ShowInTaskbar="False"
        MinHeight="200"
        MinWidth="250">
    <Border Background="#F5F5F5" BorderBrush="#CCCCCC" BorderThickness="2" CornerRadius="10">
        <Grid Margin="5">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 標題列 -->
            <Grid Grid.Row="0" Background="#E0E0E0" Height="30">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="訊息記錄"
                          FontSize="14" FontWeight="Bold"
                          VerticalAlignment="Center"
                          Margin="10,0,0,0"/>

                <Button Grid.Column="1" x:Name="CloseButton"
                       Content="✕" Width="25" Height="25"
                       Background="Transparent" BorderThickness="0"
                       FontSize="12" FontWeight="Bold"
                       Click="CloseButton_Click"
                       Cursor="Hand"/>
            </Grid>

            <!-- 訊息顯示區域 -->
            <ScrollViewer Grid.Row="1" x:Name="MessageScrollViewer"
                         VerticalScrollBarVisibility="Auto"
                         HorizontalScrollBarVisibility="Disabled"
                         Margin="5">
                <TextBlock x:Name="MessageTextBlock"
                          FontSize="20"
                          FontFamily="Microsoft JhengHei"
                          TextWrapping="Wrap"
                          Padding="5"/>
            </ScrollViewer>
        </Grid>
    </Border>
</Window>
