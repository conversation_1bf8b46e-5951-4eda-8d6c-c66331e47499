using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media.Animation;

namespace DesktopAssistantWidget
{
    public partial class TodoWindow : Window
    {
        private string todoFilePath;
        private List<TodoItem> todoItems;

        public TodoWindow()
        {
            InitializeComponent();
            InitializeTodoWindow();
        }

        private void InitializeTodoWindow()
        {
            // 設定工作檔案路徑（與MainWindow一致）
            string today = DateTime.Now.ToString("yyyy-MM-dd");
            string chatLogsDir = "chat_logs";
            todoFilePath = Path.Combine(chatLogsDir, $"工作記錄-{today}.csv");

            // 初始化工作項目清單
            todoItems = new List<TodoItem>();

            // 載入今日工作
            LoadTodayTodos();

            // 設定視窗位置（右上角）
            SetWindowPosition();

            // 設定鍵盤事件
            this.KeyDown += TodoWindow_KeyDown;

            // 設定滑鼠右鍵事件
            this.MouseRightButtonDown += TodoWindow_MouseRightButtonDown;

            // 設定淡入動畫
            this.Opacity = 0;
            var fadeInAnimation = new DoubleAnimation(0, 1, TimeSpan.FromMilliseconds(300));
            this.BeginAnimation(OpacityProperty, fadeInAnimation);
        }

        private void SetWindowPosition()
        {
            // 設定視窗在右上角
            this.Left = SystemParameters.PrimaryScreenWidth - this.Width - 20;
            this.Top = 20;
        }

        private void LoadTodayTodos()
        {
            try
            {
                // 清空現有清單避免重複
                todoItems.Clear();

                if (!File.Exists(todoFilePath))
                {
                    // 檢查前一天的工作清單，載入未完成項目
                    LoadPreviousDayUnfinishedTodos();
                }

                if (File.Exists(todoFilePath))
                {
                    System.Diagnostics.Debug.WriteLine($"載入工作記錄檔案: {todoFilePath}");
                    var lines = File.ReadAllLines(todoFilePath);
                    System.Diagnostics.Debug.WriteLine($"讀取到 {lines.Length} 行工作記錄");

                    foreach (var line in lines)
                    {
                        if (!string.IsNullOrWhiteSpace(line))
                        {
                            // 支援CSV格式（逗號分隔）和Tab分隔
                            var parts = line.Contains(',') ? line.Split(',') : line.Split('\t');
                            if (parts.Length >= 2)
                            {
                                var todoItem = new TodoItem
                                {
                                    Time = parts[0].Trim(),
                                    Content = parts[1].Trim(),
                                    IsCompleted = parts.Length > 2 && !string.IsNullOrEmpty(parts[2].Trim())
                                };
                                todoItems.Add(todoItem);
                                System.Diagnostics.Debug.WriteLine($"載入工作項目: {todoItem.Time} - {todoItem.Content}");
                            }
                        }
                    }
                    System.Diagnostics.Debug.WriteLine($"總共載入 {todoItems.Count} 個工作項目");
                }

                RefreshTodoDisplay();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"載入工作清單時發生錯誤：{ex.Message}", "錯誤",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadPreviousDayUnfinishedTodos()
        {
            try
            {
                var yesterday = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd");
                var yesterdayFile = $"工作清單{yesterday}.csv";

                if (File.Exists(yesterdayFile))
                {
                    var lines = File.ReadAllLines(yesterdayFile);
                    var unfinishedItems = new List<string>();

                    foreach (var line in lines)
                    {
                        if (!string.IsNullOrWhiteSpace(line))
                        {
                            var parts = line.Split('\t');
                            // 如果沒有完成時間戳記，表示未完成
                            if (parts.Length == 2)
                            {
                                unfinishedItems.Add(line);
                            }
                        }
                    }

                    if (unfinishedItems.Any())
                    {
                        File.WriteAllLines(todoFilePath, unfinishedItems);
                    }
                }
            }
            catch (Exception)
            {
                // 忽略前一天檔案載入錯誤
            }
        }

        private void RefreshTodoDisplay()
        {
            TodoStackPanel.Children.Clear();

            // 先顯示未完成的項目
            var uncompletedItems = todoItems.Where(t => !t.IsCompleted).ToList();
            var completedItems = todoItems.Where(t => t.IsCompleted).ToList();

            foreach (var item in uncompletedItems.Concat(completedItems))
            {
                // 創建支援自動換行的TextBlock
                var textBlock = new TextBlock
                {
                    Text = item.IsCompleted ? $"{item.Time} {item.Content} (已完成)" : $"{item.Time} {item.Content}",
                    FontSize = 20,
                    TextWrapping = TextWrapping.Wrap,
                    Margin = new Thickness(5, 0, 5, 0)
                };

                if (item.IsCompleted)
                {
                    textBlock.FontStyle = FontStyles.Italic;
                    textBlock.Opacity = 0.6;
                }

                var checkBox = new CheckBox
                {
                    Content = textBlock,
                    IsChecked = item.IsCompleted,
                    Margin = new Thickness(5, 2, 5, 2),
                    Tag = item
                };

                checkBox.Checked += CheckBox_Checked;
                checkBox.Unchecked += CheckBox_Unchecked;

                TodoStackPanel.Children.Add(checkBox);
            }
        }

        private void CheckBox_Checked(object sender, RoutedEventArgs e)
        {
            var checkBox = sender as CheckBox;
            var todoItem = checkBox.Tag as TodoItem;

            if (todoItem != null && !todoItem.IsCompleted)
            {
                todoItem.IsCompleted = true;
                SaveTodoList();
                RefreshTodoDisplay();
            }
        }

        private void CheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            var checkBox = sender as CheckBox;
            var todoItem = checkBox.Tag as TodoItem;

            if (todoItem != null && todoItem.IsCompleted)
            {
                todoItem.IsCompleted = false;
                SaveTodoList();
                RefreshTodoDisplay();
            }
        }

        private void SaveTodoList()
        {
            try
            {
                var lines = new List<string>();
                foreach (var item in todoItems)
                {
                    if (item.IsCompleted)
                    {
                        lines.Add($"{item.Time}\t{item.Content}\t{DateTime.Now:HH:mm}");
                    }
                    else
                    {
                        lines.Add($"{item.Time}\t{item.Content}");
                    }
                }
                File.WriteAllLines(todoFilePath, lines);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"儲存工作清單時發生錯誤：{ex.Message}", "錯誤",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void TodoWindow_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Escape)
            {
                HideWindow();
            }
        }

        private void TodoWindow_MouseRightButtonDown(object sender, MouseButtonEventArgs e)
        {
            // 右鍵點擊關閉視窗
            HideWindow();
            e.Handled = true;
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            HideWindow();
        }

        private void HideWindow()
        {
            var fadeOutAnimation = new DoubleAnimation(1, 0, TimeSpan.FromMilliseconds(300));
            fadeOutAnimation.Completed += (s, e) => this.Hide();
            this.BeginAnimation(OpacityProperty, fadeOutAnimation);
        }

        public void ShowWindow()
        {
            this.Show();
            var fadeInAnimation = new DoubleAnimation(0, 1, TimeSpan.FromMilliseconds(300));
            this.BeginAnimation(OpacityProperty, fadeInAnimation);
            RefreshTodos(); // 重新載入工作清單
        }

        public void RefreshTodos()
        {
            LoadTodayTodos();
        }
    }

    public class TodoItem
    {
        public string Time { get; set; }
        public string Content { get; set; }
        public bool IsCompleted { get; set; }
    }
}
