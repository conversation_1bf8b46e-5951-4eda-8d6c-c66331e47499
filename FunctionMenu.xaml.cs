using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Imaging;

namespace DesktopAssistantWidget
{
    public partial class FunctionMenu : Window
    {
        private MessageWindow messageWindow;
        private TodoWindow todoWindow;
        private List<ShortcutItem> shortcuts;
        private MainWindow mainWindow;

        public FunctionMenu(MessageWindow msgWindow, TodoWindow todoWin, MainWindow mainWin = null)
        {
            InitializeComponent();
            messageWindow = msgWindow;
            todoWindow = todoWin;
            mainWindow = mainWin;
            InitializeFunctionMenu();
        }

        private void InitializeFunctionMenu()
        {
            LoadShortcuts();
            CreateFunctionButtons();

            // 設定淡入動畫（從0到85%透明度）
            this.Opacity = 0;
            var fadeInAnimation = new DoubleAnimation(0, 0.85, TimeSpan.FromMilliseconds(300));
            this.BeginAnimation(OpacityProperty, fadeInAnimation);

            // 點擊其他地方關閉選單
            this.Deactivated += (s, e) => this.Close();

            // 設定滑鼠右鍵事件
            this.MouseRightButtonDown += FunctionMenu_MouseRightButtonDown;
        }

        private void LoadShortcuts()
        {
            shortcuts = new List<ShortcutItem>();

            try
            {
                System.Diagnostics.Debug.WriteLine("開始載入捷徑設定");

                if (File.Exists("捷徑.ini"))
                {
                    System.Diagnostics.Debug.WriteLine("找到捷徑.ini檔案");
                    var lines = File.ReadAllLines("捷徑.ini");
                    System.Diagnostics.Debug.WriteLine($"讀取到 {lines.Length} 行");

                    foreach (var line in lines)
                    {
                        if (!string.IsNullOrWhiteSpace(line) && !line.StartsWith("#"))
                        {
                            System.Diagnostics.Debug.WriteLine($"解析行: {line}");

                            // 使用逗號分隔
                            var parts = line.Split(',');
                            System.Diagnostics.Debug.WriteLine($"分隔後得到 {parts.Length} 個部分");

                            if (parts.Length >= 2)
                            {
                                shortcuts.Add(new ShortcutItem
                                {
                                    Name = parts[0].Trim(),
                                    Path = parts[1].Trim(),
                                    Description = parts.Length > 2 ? parts[2].Trim() : ""
                                });
                                System.Diagnostics.Debug.WriteLine($"載入捷徑: {parts[0].Trim()} -> {parts[1].Trim()}");
                            }
                        }
                    }
                    System.Diagnostics.Debug.WriteLine($"總共載入 {shortcuts.Count} 個捷徑");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("捷徑.ini檔案不存在，創建預設檔案");

                    // 建立預設捷徑檔案（使用逗號分隔）
                    var defaultShortcuts = new[]
                    {
                        "記事本,notepad.exe,開啟記事本",
                        "小畫家,mspaint.exe,開啟小畫家",
                        "計算機,calc.exe,開啟計算機"
                    };
                    File.WriteAllLines("捷徑.ini", defaultShortcuts);
                    LoadShortcuts(); // 重新載入
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"載入捷徑設定時發生錯誤：{ex.Message}", "錯誤",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CreateFunctionButtons()
        {
            // 系統功能按鈕陣列（每排6個，共3排，關閉小工具放在最後）
            var systemButtons = new (string icon, string name, string desc, Action action)[]
            {
                // 第一排（6個）
                ("💬", "訊息記錄", "開啟/關閉訊息記錄視窗", (Action)(() =>
                {
                    if (messageWindow.IsVisible)
                        messageWindow.Hide();
                    else
                        messageWindow.ShowWindow();

                    Dispatcher.BeginInvoke(new Action(() => this.Hide()));
                })),
                ("📝", "待辦工作", "開啟/關閉待辦工作視窗", (Action)(() =>
                {
                    if (todoWindow.IsVisible)
                        todoWindow.Hide();
                    else
                        todoWindow.ShowWindow();

                    Dispatcher.BeginInvoke(new Action(() => this.Hide()));
                })),
                ("🌐", "IP申請", "開啟IP申請表單", (Action)(() =>
                {
                    try
                    {
                        var ipWindow = new IpApplicationWindow(mainWindow);

                        if (mainWindow != null)
                        {
                            var characterCenterX = mainWindow.Left + mainWindow.Width / 2;
                            var characterTopY = mainWindow.Top;

                            ipWindow.Left = characterCenterX - ipWindow.Width / 2;
                            ipWindow.Top = characterTopY - ipWindow.Height - 10;

                            if (ipWindow.Left < 0) ipWindow.Left = 0;
                            if (ipWindow.Top < 0) ipWindow.Top = 0;
                        }

                        ipWindow.Show();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"無法開啟IP申請表單：{ex.Message}", "錯誤",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }

                    Dispatcher.BeginInvoke(new Action(() => this.Hide()));
                })),
                ("🔧", "維護通告", "開啟維護通告表單", (Action)(() =>
                {
                    try
                    {
                        var maintenanceWindow = new MaintenanceNotificationWindow(mainWindow);

                        if (mainWindow != null)
                        {
                            var characterCenterX = mainWindow.Left + mainWindow.Width / 2;
                            var characterTopY = mainWindow.Top;

                            maintenanceWindow.Left = characterCenterX - maintenanceWindow.Width / 2;
                            maintenanceWindow.Top = characterTopY - maintenanceWindow.Height - 10;

                            if (maintenanceWindow.Left < 0) maintenanceWindow.Left = 0;
                            if (maintenanceWindow.Top < 0) maintenanceWindow.Top = 0;
                        }

                        maintenanceWindow.Show();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"無法開啟維護通告表單：{ex.Message}", "錯誤",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }

                    Dispatcher.BeginInvoke(new Action(() => this.Hide()));
                })),
                ("📰", "RSS新聞", "顯示最新新聞", (Action)(() =>
                {
                    if (mainWindow != null)
                    {
                        _ = mainWindow.ShowRandomRssNews();
                    }
                    Dispatcher.BeginInvoke(new Action(() => this.Hide()));
                })),
                ("🔄", "重新載入", "重新載入設定", (Action)(() =>
                {
                    LoadShortcuts();
                    Dispatcher.BeginInvoke(new Action(() => this.Hide()));
                })),

                // 第二排（6個）
                ("ℹ️", "關於", "關於桌面小工具", (Action)(() =>
                {
                    MessageBox.Show("桌面助手小工具 v1.0.3\n\n一個可愛的桌面助手程式", "關於",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    Dispatcher.BeginInvoke(new Action(() => this.Hide()));
                })),
                ("", "", "", null), // 空位
                ("", "", "", null), // 空位
                ("", "", "", null), // 空位
                ("", "", "", null), // 空位
                ("", "", "", null), // 空位

                // 第三排（6個，最後一個是關閉小工具）
                ("", "", "", null), // 空位
                ("", "", "", null), // 空位
                ("", "", "", null), // 空位
                ("", "", "", null), // 空位
                ("", "", "", null), // 空位
                ("❌", "關閉桌面小工具", "關閉桌面小工具", (Action)(() =>
                {
                    // 先隱藏功能選單
                    this.Hide();

                    // 創建自定義確認對話框，確保顯示在角色中心
                    var confirmDialog = new Window
                    {
                        Title = "確認",
                        Width = 300,
                        Height = 150,
                        WindowStartupLocation = WindowStartupLocation.Manual,
                        ResizeMode = ResizeMode.NoResize,
                        WindowStyle = WindowStyle.ToolWindow,
                        Topmost = true
                    };

                    // 計算角色中心位置
                    if (mainWindow != null)
                    {
                        var characterCenterX = mainWindow.Left + mainWindow.Width / 2;
                        var characterCenterY = mainWindow.Top + mainWindow.Height / 2;

                        confirmDialog.Left = characterCenterX - confirmDialog.Width / 2;
                        confirmDialog.Top = characterCenterY - confirmDialog.Height / 2;

                        // 確保對話框在螢幕範圍內
                        var screenWidth = SystemParameters.PrimaryScreenWidth;
                        var screenHeight = SystemParameters.PrimaryScreenHeight;

                        if (confirmDialog.Left < 0) confirmDialog.Left = 0;
                        if (confirmDialog.Top < 0) confirmDialog.Top = 0;
                        if (confirmDialog.Left + confirmDialog.Width > screenWidth)
                            confirmDialog.Left = screenWidth - confirmDialog.Width;
                        if (confirmDialog.Top + confirmDialog.Height > screenHeight)
                            confirmDialog.Top = screenHeight - confirmDialog.Height;
                    }

                    // 創建對話框內容
                    var stackPanel = new StackPanel { Margin = new Thickness(20) };
                    var textBlock = new TextBlock
                    {
                        Text = "確定要關閉桌面小工具嗎？",
                        TextAlignment = TextAlignment.Center,
                        Margin = new Thickness(0, 0, 0, 20),
                        FontSize = 14
                    };

                    var buttonPanel = new StackPanel
                    {
                        Orientation = Orientation.Horizontal,
                        HorizontalAlignment = HorizontalAlignment.Center
                    };

                    var yesButton = new Button
                    {
                        Content = "是",
                        Width = 60,
                        Height = 30,
                        Margin = new Thickness(0, 0, 10, 0)
                    };
                    var noButton = new Button
                    {
                        Content = "否",
                        Width = 60,
                        Height = 30
                    };

                    yesButton.Click += (s, e) =>
                    {
                        confirmDialog.DialogResult = true;
                        confirmDialog.Close();
                    };

                    noButton.Click += (s, e) =>
                    {
                        confirmDialog.DialogResult = false;
                        confirmDialog.Close();
                    };

                    buttonPanel.Children.Add(yesButton);
                    buttonPanel.Children.Add(noButton);
                    stackPanel.Children.Add(textBlock);
                    stackPanel.Children.Add(buttonPanel);
                    confirmDialog.Content = stackPanel;

                    // 顯示對話框
                    var result = confirmDialog.ShowDialog();

                    if (result == true)
                    {
                        try
                        {
                            // 使用 Process.Kill 強制關閉
                            System.Diagnostics.Process.GetCurrentProcess().Kill();
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"FunctionMenu: 關閉失敗: {ex.Message}");
                            System.Environment.Exit(0);
                        }
                    }
                }))
            };

            // 分割為三排，每排6個
            var firstRowButtons = systemButtons.Take(6).ToList();
            var secondRowButtons = systemButtons.Skip(6).Take(6).ToList();
            var thirdRowButtons = systemButtons.Skip(12).Take(6).ToList();

            // 處理第二排和第三排的捷徑按鈕（替換空位）
            System.Diagnostics.Debug.WriteLine($"開始處理捷徑按鈕，共有 {shortcuts.Count} 個捷徑");

            // 第二排可以放6個捷徑
            for (int i = 0; i < 6; i++)
            {
                if (i < shortcuts.Count)
                {
                    // 創建局部變數避免閉包問題
                    var shortcutIndex = i;
                    var shortcutName = shortcuts[i].Name;
                    var shortcutPath = shortcuts[i].Path;
                    var shortcutDesc = shortcuts[i].Description;

                    System.Diagnostics.Debug.WriteLine($"創建第二排捷徑按鈕 {i}: {shortcutName} -> {shortcutPath}");

                    secondRowButtons[i] = ("🔗", shortcutName, shortcutDesc, (Action)(() =>
                    {
                        try
                        {
                            System.Diagnostics.Debug.WriteLine($"捷徑按鈕被點擊: {shortcutName}");
                            System.Diagnostics.Debug.WriteLine($"直接執行: {shortcutPath}");

                            // 直接執行命令，隱藏命令提示字元視窗
                            Process.Start(new ProcessStartInfo
                            {
                                FileName = "cmd.exe",
                                Arguments = $"/c {shortcutPath}",
                                UseShellExecute = false,
                                CreateNoWindow = true,
                                WindowStyle = ProcessWindowStyle.Hidden
                            });
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"無法開啟 {shortcutName}：{ex.Message}", "錯誤",
                                MessageBoxButton.OK, MessageBoxImage.Error);
                            System.Diagnostics.Debug.WriteLine($"執行捷徑時發生錯誤: {ex.Message}");
                        }
                        Dispatcher.BeginInvoke(new Action(() => this.Hide()));
                    }));
                }
                else
                {
                    // 保持空位
                    secondRowButtons[i] = ("", "未指定", "未指定", null);
                }
            }

            // 第三排可以放5個捷徑，最後一個是關閉小工具
            for (int i = 0; i < 5; i++)
            {
                var shortcutIndex = i + 6; // 從第7個捷徑開始
                if (shortcutIndex < shortcuts.Count)
                {
                    // 創建局部變數避免閉包問題
                    var shortcutName = shortcuts[shortcutIndex].Name;
                    var shortcutPath = shortcuts[shortcutIndex].Path;
                    var shortcutDesc = shortcuts[shortcutIndex].Description;

                    System.Diagnostics.Debug.WriteLine($"創建第三排捷徑按鈕 {i}: {shortcutName} -> {shortcutPath}");

                    thirdRowButtons[i] = ("🔗", shortcutName, shortcutDesc, (Action)(() =>
                    {
                        try
                        {
                            System.Diagnostics.Debug.WriteLine($"捷徑按鈕被點擊: {shortcutName}");
                            System.Diagnostics.Debug.WriteLine($"直接執行: {shortcutPath}");

                            // 直接執行命令，隱藏命令提示字元視窗
                            Process.Start(new ProcessStartInfo
                            {
                                FileName = "cmd.exe",
                                Arguments = $"/c {shortcutPath}",
                                UseShellExecute = false,
                                CreateNoWindow = true,
                                WindowStyle = ProcessWindowStyle.Hidden
                            });
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"無法開啟 {shortcutName}：{ex.Message}", "錯誤",
                                MessageBoxButton.OK, MessageBoxImage.Error);
                            System.Diagnostics.Debug.WriteLine($"執行捷徑時發生錯誤: {ex.Message}");
                        }
                        Dispatcher.BeginInvoke(new Action(() => this.Hide()));
                    }));
                }
                else
                {
                    // 保持空位
                    thirdRowButtons[i] = ("", "未指定", "未指定", null);
                }
            }

            // 創建第一排按鈕
            System.Diagnostics.Debug.WriteLine($"創建第一排按鈕，共 {firstRowButtons.Count} 個");
            foreach (var button in firstRowButtons)
            {
                CreateFunctionButton(button.icon, button.name, button.desc, button.action, FirstRowPanel);
                System.Diagnostics.Debug.WriteLine($"創建第一排按鈕: {button.name}");
            }

            // 創建第二排按鈕
            System.Diagnostics.Debug.WriteLine($"創建第二排按鈕，共 {secondRowButtons.Count} 個");
            foreach (var button in secondRowButtons)
            {
                CreateFunctionButton(button.icon, button.name, button.desc, button.action, SecondRowPanel);
                System.Diagnostics.Debug.WriteLine($"創建第二排按鈕: {button.name}");
            }

            // 創建第三排按鈕
            System.Diagnostics.Debug.WriteLine($"創建第三排按鈕，共 {thirdRowButtons.Count} 個");
            foreach (var button in thirdRowButtons)
            {
                CreateFunctionButton(button.icon, button.name, button.desc, button.action, ThirdRowPanel);
                System.Diagnostics.Debug.WriteLine($"創建第三排按鈕: {button.name}");
            }
        }

        private void CreateFunctionButton(string icon, string name, string description, Action action, StackPanel parentPanel = null)
        {
            var button = new Button
            {
                Width = 80,
                Height = 80,
                Margin = new Thickness(5),
                Background = new SolidColorBrush(Color.FromArgb(200, 255, 255, 255)),
                BorderBrush = new SolidColorBrush(Color.FromArgb(100, 70, 130, 180)),
                BorderThickness = new Thickness(1),
                Cursor = Cursors.Hand
            };

            var stackPanel = new StackPanel
            {
                Orientation = Orientation.Vertical,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            var iconText = new TextBlock
            {
                Text = icon,
                FontSize = 24,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 5, 0, 2)
            };

            var nameText = new TextBlock
            {
                Text = name,
                FontSize = 10,
                HorizontalAlignment = HorizontalAlignment.Center,
                TextWrapping = TextWrapping.Wrap
            };

            stackPanel.Children.Add(iconText);
            stackPanel.Children.Add(nameText);
            button.Content = stackPanel;

            // 滑鼠懸停效果
            button.MouseEnter += (s, e) =>
            {
                var scaleTransform = new ScaleTransform(1.1, 1.1);
                button.RenderTransform = scaleTransform;
                button.RenderTransformOrigin = new Point(0.5, 0.5);
                
                // 顯示提示
                button.ToolTip = $"{name}\n{description}";
            };

            button.MouseLeave += (s, e) =>
            {
                button.RenderTransform = null;
            };

            if (action != null)
            {
                button.Click += (s, e) =>
                {
                    System.Diagnostics.Debug.WriteLine($"按鈕被點擊: {name}");
                    action();
                };
            }
            else
            {
                button.IsEnabled = false;
                System.Diagnostics.Debug.WriteLine($"按鈕無動作: {name}");
            }

            // 添加到指定的父容器，如果沒有指定則添加到FirstRowPanel
            if (parentPanel != null)
                parentPanel.Children.Add(button);
            else
                FirstRowPanel.Children.Add(button);
        }

        public void SetPosition(Point position)
        {
            this.Left = position.X;
            this.Top = position.Y;
        }

        private void FunctionMenu_MouseRightButtonDown(object sender, MouseButtonEventArgs e)
        {
            // 右鍵點擊關閉選單
            e.Handled = true;
            this.Hide();
        }
    }

    public class ShortcutItem
    {
        public string Name { get; set; }
        public string Path { get; set; }
        public string Description { get; set; }
    }
}
