# 桌面助手小工具 (Desktop Assistant Widget)

一個基於WPF的桌面寵物應用程式，具備多種實用功能的偽春菜/Ukagaka風格桌面助手。

## 📋 目錄

- [功能特色](#功能特色)
- [系統需求](#系統需求)
- [安裝說明](#安裝說明)
- [使用指南](#使用指南)
- [檔案結構](#檔案結構)
- [設定檔說明](#設定檔說明)
- [開發資訊](#開發資訊)

## ✨ 功能特色

### 🎭 角色互動系統
- **動畫系統**：包含待機動畫(stand1-5.png)、隨機動畫(random1_1-5)、眨眼動畫(blink.png)
- **拖拽功能**：支援拖拽移動，包含拖拽狀態動畫(drag.png, drag_left.png, drag_right.png)
- **邊界檢測**：角色自動保持在螢幕範圍內
- **個性化對話**：從Dialogue.txt載入多種對話類別

### 💬 對話系統
- **智能回覆**：根據輸入類型提供不同回覆
- **多語言支援**：法文對話配中文翻譯
- **隨機回覆**：從[random_normal]類別隨機選擇回覆
- **自動換行**：支援\n換行符號處理

### 🧮 計算功能
- **數學運算**：支援基本四則運算(+, -, *, /)
- **智能識別**：自動識別數學表達式
- **結果記錄**：格式為`[時間] 表達式=結果`
- **個性化回覆**：從[response_calculation]類別隨機選擇回覆

### 📝 工作記錄系統
- **工作任務**：輸入"工"開頭文字記錄工作任務
- **自動處理**：去掉"工"字前綴後儲存
- **CSV格式**：儲存為`工作記錄-yyyy-mm-dd.csv`
- **視窗顯示**：待辦工作視窗即時更新
- **確認回覆**：從[response_work_record]類別隨機選擇回覆

### 📰 RSS新聞功能
- **自動載入**：定期載入RSS新聞
- **智能顯示**：新聞標題+法文對話回覆
- **寬度調整**：根據法文長度自動調整視窗寬度
- **連結開啟**：點擊回覆訊息用Edge開啟新聞連結
- **個性化回覆**：從[response_news]類別隨機選擇回覆

### 📧 維護通告系統
- **表單填寫**：完整的維護通告資訊輸入
- **Excel產出**：自動生成維護通告Excel檔案
- **信件產出**：可選擇是否產出Outlook信件
- **範本支援**：使用`範本_維護通告.txt`範本
- **智能保留**：不勾選產出信件時保留表單內容

### 🏢 IP申請功能
- **申請表單**：完整的IP申請資訊輸入
- **自動編號**：依據日期和序號自動生成申請編號
- **Excel產出**：生成標準格式的IP申請Excel檔案
- **範本支援**：使用`範本_IP申請.txt`範本

### 📊 記錄管理
- **聊天記錄**：所有對話記錄到`chat_logs/yyyy-mm-dd.txt`
- **工作記錄**：工作任務記錄到`工作記錄-yyyy-mm-dd.csv`
- **訊息視窗**：即時查看聊天記錄
- **待辦視窗**：管理工作任務清單

### 🎛️ 右鍵選單
- **快捷功能**：2行6列共12個快捷功能
- **自訂捷徑**：從`捷徑.ini`載入自訂程式捷徑
- **系統功能**：包含RSS新聞、維護通告、IP申請等
- **視窗管理**：開啟各種功能視窗

## 🖥️ 系統需求

- **作業系統**：Windows 10/11
- **框架**：.NET 6.0 或更高版本
- **記憶體**：至少 100MB 可用記憶體
- **磁碟空間**：至少 50MB 可用空間

## 📦 安裝說明

1. **下載程式**：
   ```
   git clone [repository-url]
   cd DesktopAssistantWidget
   ```

2. **編譯程式**：
   ```bash
   dotnet build
   ```

3. **執行程式**：
   ```bash
   dotnet run
   ```

## 📖 使用指南

### 基本操作

#### 🖱️ 滑鼠操作
- **雙擊角色**：開啟輸入對話框
- **右鍵角色**：開啟功能選單
- **拖拽角色**：移動角色位置
- **右鍵視窗**：關閉各種功能視窗

#### ⌨️ 鍵盤操作
- **ESC鍵**：關閉聊天記錄或工作記錄視窗
- **Enter鍵**：確認輸入對話
- **右鍵**：關閉輸入對話框或右鍵選單

### 功能使用

#### 💬 對話功能
1. 雙擊角色開啟輸入框
2. 輸入文字後按Enter或點擊確認
3. 角色會根據輸入類型給予不同回覆

#### 🧮 計算功能
1. 在對話框中輸入數學表達式（如：`2+3`、`10*5`）
2. 系統自動識別並計算結果
3. 顯示個性化計算回覆
4. 結果記錄到聊天記錄檔案

#### 📝 工作記錄
1. 在對話框中輸入"工"開頭的文字（如：`工完成報告`）
2. 系統去掉"工"字後記錄任務內容
3. 顯示工作確認回覆
4. 任務記錄到工作記錄檔案

#### 📰 RSS新聞
1. 右鍵角色選擇「📰 RSS新聞」
2. 系統顯示新聞標題和個性化回覆
3. 點擊回覆訊息開啟新聞連結

#### 📧 維護通告
1. 右鍵角色選擇「📧 維護通告」
2. 填寫維護通告表單
3. 選擇是否產出信件
4. 點擊送出生成Excel和信件（可選）

#### 🏢 IP申請
1. 右鍵角色選擇「🏢 IP申請」
2. 填寫IP申請表單
3. 系統自動生成申請編號
4. 點擊送出生成Excel檔案

## 📁 檔案結構

```
DesktopAssistantWidget/
├── Images/                     # 角色圖片資源
│   ├── stand1.png - stand5.png # 待機動畫
│   ├── random1_1.png - random1_5.png # 隨機動畫
│   ├── blink.png              # 眨眼動畫
│   ├── drag.png               # 拖拽動畫
│   ├── drag_left.png          # 左拖拽動畫
│   └── drag_right.png         # 右拖拽動畫
├── chat_logs/                  # 記錄檔案目錄
│   ├── yyyy-mm-dd.txt         # 每日聊天記錄
│   └── 工作記錄-yyyy-mm-dd.csv # 每日工作記錄
├── Dialogue.txt               # 對話內容檔案
├── 捷徑.ini                   # 右鍵選單捷徑設定
├── 範本_維護通告.txt          # 維護通告信件範本
├── 範本_IP申請.txt            # IP申請信件範本
└── README.md                  # 說明文件
```

## ⚙️ 設定檔說明

### Dialogue.txt 對話設定
包含多種對話類別：
- `[random_normal]`：一般隨機回覆
- `[response_work_record]`：工作記錄確認回覆
- `[response_calculation]`：計算結果回覆
- `[response_news]`：新聞回覆

### 捷徑.ini 選單設定
格式：`名稱\t開啟路徑\t程式說明`
```ini
記事本	notepad.exe	開啟記事本
計算機	calc.exe	開啟計算機
```

## 🛠️ 開發資訊

### 技術架構
- **框架**：WPF (.NET 6.0)
- **語言**：C#
- **UI設計**：XAML
- **檔案格式**：CSV, Excel, TXT

### 主要類別
- `MainWindow`：主視窗和核心邏輯
- `ReplyWindow`：回覆訊息視窗
- `TodoWindow`：待辦工作視窗
- `MessageWindow`：聊天記錄視窗
- `MaintenanceNotificationWindow`：維護通告視窗
- `IpApplicationWindow`：IP申請視窗

### 特色功能
- **動畫系統**：基於DispatcherTimer的動畫控制
- **檔案管理**：自動建立和管理記錄檔案
- **視窗定位**：智能視窗位置計算和邊界檢測
- **多語言顯示**：法文+中文雙語顯示
- **Excel整合**：使用ClosedXML生成Excel檔案

---

**版本**：1.0.0
**開發者**：Desktop Assistant Team
**授權**：MIT License
