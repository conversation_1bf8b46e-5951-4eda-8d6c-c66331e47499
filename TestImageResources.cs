using System;
using System.Reflection;
using System.Windows.Media.Imaging;

namespace DesktopAssistantWidget
{
    public static class TestImageResources
    {
        public static void TestAllImages()
        {
            Console.WriteLine("=== 測試圖片資源載入 ===");
            
            // 列出所有嵌入的資源
            var assembly = Assembly.GetExecutingAssembly();
            var resourceNames = assembly.GetManifestResourceNames();
            
            Console.WriteLine("嵌入的資源:");
            foreach (var name in resourceNames)
            {
                Console.WriteLine($"  - {name}");
            }
            
            Console.WriteLine("\n=== 測試圖片載入 ===");
            
            // 測試待機動畫圖片
            for (int i = 1; i <= 5; i++)
            {
                TestLoadImage($"stand{i}.png");
            }
            
            // 測試其他圖片
            TestLoadImage("drag.png");
            TestLoadImage("drag_left.png");
            TestLoadImage("drag_right.png");
            TestLoadImage("blink.png");
            
            // 測試隨機動畫圖片
            for (int i = 1; i <= 5; i++)
            {
                TestLoadImage($"random1_{i}.png");
            }
        }
        
        private static void TestLoadImage(string fileName)
        {
            try
            {
                var uri = new Uri($"pack://application:,,,/Assets/{fileName}");
                var bitmap = new BitmapImage(uri);
                Console.WriteLine($"✅ {fileName} - 載入成功 ({bitmap.PixelWidth}x{bitmap.PixelHeight})");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ {fileName} - 載入失敗: {ex.Message}");
            }
        }
    }
}
