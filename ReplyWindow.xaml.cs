using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Media.Animation;
using System.Windows.Threading;

namespace DesktopAssistantWidget
{
    public partial class ReplyWindow : Window
    {
        private DispatcherTimer autoCloseTimer;

        public ReplyWindow(string message, bool isLink = false, string linkUrl = "")
        {
            InitializeComponent();

            // 設定視窗不搶奪焦點
            this.ShowActivated = false;
            this.Focusable = false;

            InitializeReplyWindow(message, isLink, linkUrl);
        }

        private void InitializeReplyWindow(string message, bool isLink, string linkUrl = "")
        {
            // 處理\n換行符號
            var processedMessage = message.Replace("\\n", "\n");
            ReplyTextBlock.Text = processedMessage;

            // 根據法文長度設定視窗寬度
            SetWindowWidthBasedOnFrench(message);

            // 如果是連結，設定點擊事件
            if (isLink && !string.IsNullOrEmpty(linkUrl))
            {
                ReplyTextBlock.Cursor = System.Windows.Input.Cursors.Hand;
                ReplyTextBlock.MouseLeftButtonDown += (s, e) =>
                {
                    try
                    {
                        // 優先使用Edge開啟連結
                        Process.Start(new ProcessStartInfo
                        {
                            FileName = "msedge.exe",
                            Arguments = linkUrl,
                            UseShellExecute = true
                        });
                        System.Diagnostics.Debug.WriteLine($"用Edge開啟新聞: {linkUrl}");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"開啟Edge失敗: {ex.Message}");
                        // 如果Edge開啟失敗，使用預設瀏覽器
                        try
                        {
                            Process.Start(new ProcessStartInfo
                            {
                                FileName = linkUrl,
                                UseShellExecute = true
                            });
                        }
                        catch (Exception ex2)
                        {
                            MessageBox.Show($"無法開啟連結：{ex2.Message}", "錯誤",
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                };
            }

            // 設定淡入動畫
            this.Opacity = 0;
            var fadeInAnimation = new DoubleAnimation(0, 1, TimeSpan.FromMilliseconds(300));
            this.BeginAnimation(OpacityProperty, fadeInAnimation);

            // 設定自動關閉計時器 (5秒後淡出)
            autoCloseTimer = new DispatcherTimer();
            autoCloseTimer.Interval = TimeSpan.FromSeconds(5);
            autoCloseTimer.Tick += AutoCloseTimer_Tick;
            autoCloseTimer.Start();
        }

        private void AutoCloseTimer_Tick(object sender, EventArgs e)
        {
            autoCloseTimer.Stop();
            FadeOutAndClose();
        }

        private void FadeOutAndClose()
        {
            var fadeOutAnimation = new DoubleAnimation(1, 0, TimeSpan.FromMilliseconds(500));
            fadeOutAnimation.Completed += (s, e) => this.Close();
            this.BeginAnimation(OpacityProperty, fadeOutAnimation);
        }

        public void SetPosition(Point position)
        {
            this.Left = position.X;
            this.Top = position.Y;
        }

        private void SetWindowWidthBasedOnFrench(string message)
        {
            try
            {
                // 提取法文部分（在« »之間的內容）
                var frenchText = ExtractFrenchText(message);

                if (!string.IsNullOrEmpty(frenchText))
                {
                    // 根據法文長度計算寬度
                    var frenchLength = frenchText.Length;

                    // 基礎寬度 + 每個字符的寬度
                    var baseWidth = 100; // 基礎寬度
                    var charWidth = 8;   // 每個字符的平均寬度
                    var calculatedWidth = baseWidth + (frenchLength * charWidth);

                    // 設定最小和最大寬度
                    var minWidth = 150;
                    var maxWidth = 400;

                    var finalWidth = Math.Max(minWidth, Math.Min(maxWidth, calculatedWidth));

                    // 設定Border的寬度
                    ReplyBorder.Width = finalWidth;

                    System.Diagnostics.Debug.WriteLine($"法文: {frenchText}, 長度: {frenchLength}, 寬度: {finalWidth}");
                }
                else
                {
                    // 如果沒有法文，使用預設寬度
                    ReplyBorder.Width = 200;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"設定寬度失敗: {ex.Message}");
                ReplyBorder.Width = 200; // 預設寬度
            }
        }

        private string ExtractFrenchText(string message)
        {
            try
            {
                // 尋找« »之間的內容
                var startIndex = message.IndexOf("«");
                var endIndex = message.IndexOf("»");

                if (startIndex >= 0 && endIndex > startIndex)
                {
                    var frenchPart = message.Substring(startIndex + 1, endIndex - startIndex - 1).Trim();

                    // 移除換行符號，取得完整法文內容長度
                    frenchPart = frenchPart.Replace("\\n", " ").Replace("\n", " ");

                    return frenchPart;
                }

                return "";
            }
            catch
            {
                return "";
            }
        }
    }
}
