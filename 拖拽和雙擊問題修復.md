# 🔧 拖拽和雙擊問題修復

## 🐛 問題描述

### 問題 1: 往右拖曳沒有載入drag_right.png
- **現象**: 向右拖拽時沒有顯示 `drag_right.png`
- **原因**: 拖拽方向檢測邏輯錯誤

### 問題 2: 雙擊後角色停止不動且不顯示輸入框
- **現象**: 雙擊角色後動畫停止，輸入框不出現
- **原因**: 雙擊檢測與拖拽邏輯衝突

## ✅ 修復方案

### 修復 1: 改進拖拽方向檢測

#### 🔧 問題分析
原始邏輯使用滑鼠相對位置計算方向，但這個方法不準確：
```csharp
// 錯誤的方法
var currentPos = e.GetPosition(this);
var deltaX = currentPos.X - (dragStartPosition.X - this.Left);
```

#### ✨ 新的解決方案
使用視窗絕對位置計算移動方向：
```csharp
// 正確的方法
var currentWindowPos = new Point(this.Left, this.Top);
var deltaX = currentWindowPos.X - dragStartPosition.X;
var deltaY = currentWindowPos.Y - dragStartPosition.Y;

System.Diagnostics.Debug.WriteLine($"拖拽移動: deltaX={deltaX}, deltaY={deltaY}");

// 根據移動方向即時更新動畫
if (Math.Abs(deltaX) > 10) // 移動距離超過10像素才判斷方向
{
    if (deltaX > 0)
    {
        // 向右拖拽
        SetAnimationState(AnimationState.DragRight);
        System.Diagnostics.Debug.WriteLine("即時切換到右拖動畫");
    }
    else
    {
        // 向左拖拽
        SetAnimationState(AnimationState.DragLeft);
        System.Diagnostics.Debug.WriteLine("即時切換到左拖動畫");
    }
}
```

#### 🎯 改進內容
- **精確計算**: 使用視窗絕對位置而非相對位置
- **閾值調整**: 移動距離超過10像素才判斷方向
- **Debug 輸出**: 詳細記錄拖拽過程
- **即時響應**: 移動時立即切換動畫

### 修復 2: 改進雙擊檢測邏輯

#### 🔧 問題分析
原始邏輯中雙擊檢測與拖拽處理衝突：
- 雙擊後立即開始拖拽處理
- 動畫狀態被拖拽邏輯覆蓋
- 輸入框顯示被阻擋

#### ✨ 新的解決方案
分離雙擊和拖拽邏輯：
```csharp
// 檢查是否為雙擊 (150-500ms之間)
if (timeSinceLastClick > 150 && timeSinceLastClick < 500)
{
    System.Diagnostics.Debug.WriteLine("檢測到雙擊，開啟輸入框");
    e.Handled = true;
    isDragging = false; // 確保不會進入拖拽狀態
    
    // 使用 Dispatcher 延遲執行，確保動畫不會停止
    Dispatcher.BeginInvoke(new Action(() =>
    {
        ShowInputDialog();
    }), DispatcherPriority.Background);
    
    lastClickTime = DateTime.MinValue; // 重置避免三擊
    return;
}

// 延遲開始拖拽，給雙擊檢測留時間
var dragDelayTimer = new DispatcherTimer();
dragDelayTimer.Interval = TimeSpan.FromMilliseconds(300);
dragDelayTimer.Tick += (s, args) =>
{
    dragDelayTimer.Stop();
    
    // 檢查是否還在按住滑鼠且沒有觸發雙擊
    if (Mouse.LeftButton == MouseButtonState.Pressed && !isDragging)
    {
        StartDragging();
    }
};
dragDelayTimer.Start();
```

#### 🎯 改進內容
- **時間間隔調整**: 150-500ms 更適合雙擊檢測
- **狀態隔離**: 雙擊時明確設定 `isDragging = false`
- **延遲執行**: 使用 Dispatcher 確保動畫不停止
- **延遲拖拽**: 給雙擊檢測300ms的時間窗口

### 修復 3: 增強Debug輸出

#### 🔍 動畫狀態追蹤
```csharp
private void SetAnimationState(AnimationState newState)
{
    if (currentAnimationState != newState)
    {
        System.Diagnostics.Debug.WriteLine($"動畫狀態變更: {currentAnimationState} -> {newState}");
        currentAnimationState = newState;
        currentFrame = 0;

        var imageList = GetCurrentImageList();
        if (imageList != null && imageList.Count > 0)
        {
            characterImage.Source = imageList[0];
            System.Diagnostics.Debug.WriteLine($"設定動畫圖片: {newState}, 圖片數量: {imageList.Count}");
        }
        else
        {
            System.Diagnostics.Debug.WriteLine($"警告: {newState} 狀態沒有可用圖片");
        }
    }
}
```

#### 🔍 拖拽過程追蹤
```csharp
System.Diagnostics.Debug.WriteLine($"拖拽移動: deltaX={deltaX}, deltaY={deltaY}");
System.Diagnostics.Debug.WriteLine($"開始拖拽，起始位置: ({originalPosition.X}, {originalPosition.Y})");
```

## 🎯 預期效果

### 拖拽動畫
1. **開始拖拽**: 顯示 `drag.png`
2. **向右移動**: 即時切換到 `drag_right.png`
3. **向左移動**: 即時切換到 `drag_left.png`
4. **結束拖拽**: 回到待機動畫

### 雙擊功能
1. **快速雙擊**: 立即出現輸入框
2. **動畫繼續**: 角色動畫不會停止
3. **無衝突**: 不會觸發拖拽動作

## 📋 測試方法

### 測試拖拽動畫
1. 按住滑鼠左鍵在角色上
2. 向右拖拽 → 應該看到 `drag_right.png`
3. 向左拖拽 → 應該看到 `drag_left.png`
4. 放開滑鼠 → 回到待機動畫

### 測試雙擊功能
1. 快速雙擊角色圖片
2. 應該立即出現輸入框
3. 角色動畫應該繼續播放
4. 不應該觸發拖拽

### Debug 輸出檢查
在 Visual Studio 輸出視窗中應該看到：
```
滑鼠點擊，距離上次點擊: 250ms
檢測到雙擊，開啟輸入框

開始拖拽，起始位置: (100, 100)
拖拽移動: deltaX=15, deltaY=5
動畫狀態變更: Drag -> DragRight
設定動畫圖片: DragRight, 圖片數量: 1
即時切換到右拖動畫
```

## 🔧 技術細節

### 關鍵改進
1. **精確的方向檢測**: 使用視窗絕對位置
2. **事件隔離**: 雙擊和拖拽邏輯完全分離
3. **延遲處理**: 給雙擊檢測足夠時間
4. **狀態管理**: 清晰的動畫狀態控制

### 容錯機制
- 圖片載入失敗時的警告訊息
- 拖拽異常的捕獲和記錄
- 動畫狀態的安全切換

## 🎉 修復完成

兩個主要問題都已修復：
- ✅ 右拖動畫正常顯示
- ✅ 雙擊輸入框正常工作
- ✅ 動畫不會停止
- ✅ 詳細的Debug輸出

程式現在應該能提供流暢的拖拽和雙擊體驗！
