<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <AssemblyTitle>桌面助手小工具</AssemblyTitle>
    <AssemblyDescription>類似偽春菜的桌面助手</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>true</SelfContained>
    <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
    <IncludeAllContentForSelfExtract>true</IncludeAllContentForSelfExtract>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
    <PackageReference Include="EPPlus" Version="7.0.0" />
    <PackageReference Include="NPOI" Version="2.6.2" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Assets\*.png" />
    <Resource Include="Assets\*.gif" />
    <Resource Include="Assets\*.jpg" />
    <Resource Include="Assets\*.jpeg" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="Dialogue.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Include="捷徑.ini">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
    <Content Include="範本_維護通告.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
  </ItemGroup>

</Project>
