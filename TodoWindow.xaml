<Window x:Class="DesktopAssistantWidget.TodoWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="待辦工作" Height="350" Width="400"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        Topmost="True"
        ResizeMode="CanResize"
        ShowInTaskbar="False"
        MinHeight="200"
        MinWidth="300">
    <Border Background="#F0F8FF" BorderBrush="#4682B4" BorderThickness="2" CornerRadius="10" Opacity="0.9">
        <Grid Margin="5">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- 標題列 -->
            <Grid Grid.Row="0" Background="#E6F3FF" Height="30">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="今日待辦工作" 
                          FontSize="14" FontWeight="Bold" 
                          VerticalAlignment="Center" 
                          Margin="10,0,0,0"/>
                
                <Button Grid.Column="1" x:Name="CloseButton" 
                       Content="✕" Width="25" Height="25" 
                       Background="Transparent" BorderThickness="0"
                       FontSize="12" FontWeight="Bold"
                       Click="CloseButton_Click"
                       Cursor="Hand"/>
            </Grid>
            
            <!-- 工作清單區域 -->
            <ScrollViewer Grid.Row="1" 
                         VerticalScrollBarVisibility="Auto"
                         HorizontalScrollBarVisibility="Disabled"
                         Margin="5">
                <StackPanel x:Name="TodoStackPanel" Orientation="Vertical"/>
            </ScrollViewer>
        </Grid>
    </Border>
</Window>
