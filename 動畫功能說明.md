# 偽春菜動畫系統功能說明

## 新增的動畫功能

### 1. 待機動畫 (Idle Animation)
- **圖片**: `stand1.png` ~ `stand5.png`
- **播放速度**: 每0.3秒切換一張圖片
- **循環**: 播放完5張圖片後回到第1張，無限循環
- **觸發條件**: 預設狀態，所有其他動畫播放完畢後回到此狀態

### 2. 拖拽動畫 (Drag Animation)
- **圖片**: `drag.png`
- **觸發條件**: 滑鼠左鍵按住角色圖片時
- **行為**: 顯示拖拽狀態的圖片

### 3. 方向拖拽動畫 (Directional Drag Animation)
- **左拖動畫**: `drag_left.png`
- **右拖動畫**: `drag_right.png`
- **觸發條件**: 拖拽結束時，根據拖拽方向顯示對應動畫
- **判斷標準**: 移動距離超過10像素才判斷方向
- **持續時間**: 顯示0.5秒後回到待機狀態

### 4. 隨機動畫 (Random Animations)
- **動畫1**: 使用 `drop1.png` ~ `drop5.png`
- **動畫2**: 使用 `random2_1.png` ~ `random2_5.png` (如果存在)
- **動畫3**: 使用 `random3_1.png` ~ `random3_5.png` (如果存在)
- **播放速度**: 每0.3秒切換一張圖片
- **觸發頻率**: 每10秒隨機觸發一種動畫
- **觸發條件**: 僅在待機狀態時觸發
- **循環**: 播放完畢後回到待機狀態

### 5. 眨眼動畫 (Blink Animation)
- **圖片**: `blink.png`
- **觸發頻率**: 每6秒觸發一次
- **顯示時間**: 0.3秒
- **觸發條件**: 僅在待機狀態時觸發
- **行為**: 暫時替換當前圖片，然後恢復原圖片

### 6. 右鍵選單位置調整
- **新位置**: 功能選單現在出現在角色圖片的正中央
- **原位置**: 之前是在角色圖片2/3高度處

## 圖片資源需求

### 必需圖片
- `stand1.png` ~ `stand5.png` - 待機動畫
- `drag.png` - 拖拽狀態
- `drag_left.png` - 左拖動畫
- `drag_right.png` - 右拖動畫
- `blink.png` - 眨眼動畫

### 可選圖片 (用於擴展隨機動畫)
- `random2_1.png` ~ `random2_5.png` - 隨機動畫2
- `random3_1.png` ~ `random3_5.png` - 隨機動畫3

### 備用機制
- 如果 `random2_*.png` 不存在，會使用待機動畫的反向播放
- 如果 `random3_*.png` 不存在，會重複使用隨機動畫1
- 如果任何圖片載入失敗，系統會優雅地處理錯誤

## 動畫狀態管理

系統使用狀態機管理動畫：
- `Idle` - 待機狀態
- `Drag` - 拖拽狀態  
- `DragLeft` - 左拖狀態
- `DragRight` - 右拖狀態
- `Random1/2/3` - 隨機動畫狀態
- `Blink` - 眨眼狀態

## 使用方式

1. **待機**: 程式啟動後自動播放待機動畫
2. **拖拽**: 滑鼠左鍵按住角色即可拖拽，會顯示拖拽動畫
3. **方向動畫**: 拖拽結束後根據移動方向自動播放對應動畫
4. **隨機動畫**: 每10秒自動隨機播放一種動畫
5. **眨眼**: 每6秒自動眨眼一次
6. **右鍵選單**: 右鍵點擊角色圖片，選單會在圖片中央出現

## 技術特點

- 所有動畫都是非阻塞的，不會影響其他功能
- 使用 DispatcherTimer 確保動畫在UI線程中執行
- 智能的狀態管理，確保動畫之間的平滑切換
- 錯誤處理機制，缺少圖片時不會崩潰
- 可擴展的設計，容易添加新的動畫類型
