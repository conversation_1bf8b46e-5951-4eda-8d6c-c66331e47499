桌面助手小工具 v1.0.2 - 使用說明
================================

🎉 歡迎使用桌面助手小工具！

📦 版本更新
----------
v1.0.2 更新內容：
✅ 修復捷徑功能，支援逗號分隔格式
✅ 隱藏捷徑執行時的命令提示字元視窗
✅ 所有DLL和圖片資源已打包進執行檔
✅ 發佈包更精簡，只保留必要檔案
✅ 確認對話框顯示在角色中心
✅ 優化程式啟動和關閉流程

📦 安裝說明
----------
1. 將整個資料夾複製到您想要的位置
2. 雙擊 DesktopAssistantWidget.exe 執行程式
3. 首次執行會自動建立 chat_logs 資料夾

🖱️ 基本操作
----------
- 雙擊角色：開啟輸入對話框
- 右鍵角色：開啟功能選單
- 拖拽角色：移動角色位置
- ESC鍵：關閉視窗
- Ctrl+Shift+Q：強制關閉程式

💬 對話功能
----------
- 輸入一般文字：角色會給予隨機回覆
- 輸入數學表達式（如 2+3）：自動計算並顯示結果
- 輸入"工"開頭文字（如 工完成報告）：記錄工作任務

📰 RSS新聞
----------
右鍵選單 → 📰 RSS新聞
- 顯示最新新聞標題和個性化回覆
- 點擊回覆訊息開啟新聞連結

📧 維護通告
----------
右鍵選單 → 📧 維護通告
- 填寫維護通告表單
- 可選擇是否產出信件
- 自動生成Excel檔案

🏢 IP申請
--------
右鍵選單 → 🏢 IP申請
- 填寫IP申請表單
- 自動生成申請編號
- 產出Excel申請檔案

📊 記錄管理
----------
- 💬 訊息記錄：查看聊天記錄
- 📋 待辦工作：管理工作任務

⚙️ 設定檔案
----------
- Dialogue.txt：對話內容設定
- 捷徑.ini：右鍵選單捷徑設定（逗號分隔格式）
- 範本_維護通告.txt：維護通告範本

🔧 捷徑設定
----------
編輯 捷徑.ini 檔案來自訂右鍵選單捷徑：
格式：名稱,執行路徑,說明
範例：
記事本,notepad.exe,開啟記事本
聊天記錄資料夾,explorer.exe "C:\path\to\folder",開啟資料夾

📁 檔案說明
----------
- DesktopAssistantWidget.exe：主程式（已包含所有DLL和圖片資源）
- Dialogue.txt：對話設定檔
- 捷徑.ini：右鍵選單設定
- 範本_維護通告.txt：維護通告範本
- 使用說明.txt：本說明檔案
- chat_logs/：記錄檔案資料夾（自動建立）

🔧 故障排除
----------
1. 如果程式無法啟動，請確認已安裝 .NET 6.0 Runtime
2. 如果缺少設定檔，程式會使用預設值
3. 記錄檔案會自動建立在 chat_logs 資料夾
4. 如果右鍵選單無法關閉程式，請使用 Ctrl+Shift+Q
5. 捷徑執行不會顯示命令提示字元視窗

📞 技術支援
----------
如有問題請參考 README.md 詳細說明文件

版本：1.0.2
開發：Desktop Assistant Team
