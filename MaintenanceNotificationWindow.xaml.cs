using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.Globalization;

namespace DesktopAssistantWidget
{
    public partial class MaintenanceNotificationWindow : Window
    {
        private MainWindow mainWindow;

        public MaintenanceNotificationWindow(MainWindow parent)
        {
            InitializeComponent();
            mainWindow = parent;
            InitializeWindow();
        }

        private void InitializeWindow()
        {
            // 設定EPPlus授權
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            // 設定視窗位置
            this.WindowStartupLocation = WindowStartupLocation.Manual;

            // 初始化時間選項
            InitializeTimeOptions();

            // 設定明天日期
            DatePicker.SelectedDate = DateTime.Now.AddDays(1);

            // 設定焦點到第一個輸入框
            this.Loaded += (s, e) => NotifyUnitTextBox.Focus();

            // 創建範本文件（如果不存在）
            CreateTemplateFileIfNotExists();
        }

        private void InitializeTimeOptions()
        {
            // 生成30小時制，每30分鐘一個選項（00:00-30:00，共60個選項）
            for (int hour = 0; hour <= 30; hour++)
            {
                for (int minute = 0; minute < 60; minute += 30)
                {
                    // 30:30不存在，所以當hour=30時只加30:00
                    if (hour == 30 && minute == 30) break;

                    var timeString = $"{hour:D2}:{minute:D2}";
                    StartTimeComboBox.Items.Add(timeString);
                    EndTimeComboBox.Items.Add(timeString);
                }
            }

            // 設定預設值：施工時間17:30，結束時間23:30
            StartTimeComboBox.SelectedItem = "17:30";
            EndTimeComboBox.SelectedItem = "23:30";
        }

        private void CreateTemplateFileIfNotExists()
        {
            var templatePath = "範本_維護通告.txt";
            if (!File.Exists(templatePath))
            {
                var templateContent = @"*收件人：<EMAIL>;<EMAIL>
*副本收件人：<EMAIL>;<EMAIL>
標題：維護通告_（日期）（影響單位）（影響項目）
內文：
（通知單位）通知（施工單位）（事由）
施工日期為
	（日期1）（施工時間1）
	（日期2）（施工時間2）
	（日期3）（施工時間3）
影響如下：
	（影響單位）（影響項目）";

                try
                {
                    File.WriteAllText(templatePath, templateContent, System.Text.Encoding.UTF8);
                    System.Diagnostics.Debug.WriteLine($"創建範本文件: {templatePath}");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"創建範本文件失敗: {ex.Message}");
                }
            }
        }

        private void SubmitButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateForm())
            {
                try
                {
                    // 創建維護通告記錄
                    var selectedDate = DatePicker.SelectedDate ?? DateTime.Now.AddDays(1);
                    var dateString = selectedDate.ToString("MMdd");

                    var notification = new MaintenanceNotification
                    {
                        Type = ((ComboBoxItem)TypeComboBox.SelectedItem).Content.ToString(),
                        Date = dateString,
                        NotifyUnit = NotifyUnitTextBox.Text.Trim(),
                        ConstructionUnit = ConstructionUnitTextBox.Text.Trim(),
                        AffectedUnit = AffectedUnitTextBox.Text.Trim(),
                        StartTime = StartTimeComboBox.SelectedItem?.ToString() ?? "09:00",
                        EndTime = EndTimeComboBox.SelectedItem?.ToString() ?? "18:00",
                        AffectedItems = AffectedItemsTextBox.Text.Trim(),
                        Reason = ReasonTextBox.Text.Trim(),
                        GenerateEmail = GenerateEmailCheckBox.IsChecked ?? false,
                        NotificationTime = DateTime.Now,
                        SelectedDate = selectedDate
                    };

                    // 生成Excel文件
                    GenerateExcelFile(notification);

                    // 添加到工作記錄
                    AddToWorkRecord(notification);

                    // 如果勾選產出信件，生成Outlook信件
                    if (notification.GenerateEmail)
                    {
                        GenerateOutlookEmail(notification);
                    }

                    // 顯示成功訊息
                    MessageBox.Show("維護通告提交成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);

                    // 只有勾選產出信件時才清除表單，否則保留內容供後續填寫
                    if (notification.GenerateEmail)
                    {
                        ClearForm();
                    }

                    System.Diagnostics.Debug.WriteLine($"維護通告提交成功: {notification.Type} - {notification.Date}");
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"提交失敗：{ex.Message}", "錯誤", MessageBoxButton.OK, MessageBoxImage.Error);
                    System.Diagnostics.Debug.WriteLine($"維護通告提交失敗: {ex.Message}");
                }
            }
        }

        private bool ValidateForm()
        {
            if (TypeComboBox.SelectedItem == null)
            {
                MessageBox.Show("請選擇類型", "驗證失敗", MessageBoxButton.OK, MessageBoxImage.Warning);
                TypeComboBox.Focus();
                return false;
            }

            if (DatePicker.SelectedDate == null)
            {
                MessageBox.Show("請選擇日期", "驗證失敗", MessageBoxButton.OK, MessageBoxImage.Warning);
                DatePicker.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(NotifyUnitTextBox.Text))
            {
                MessageBox.Show("請填寫通知單位", "驗證失敗", MessageBoxButton.OK, MessageBoxImage.Warning);
                NotifyUnitTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(ConstructionUnitTextBox.Text))
            {
                MessageBox.Show("請填寫施工單位", "驗證失敗", MessageBoxButton.OK, MessageBoxImage.Warning);
                ConstructionUnitTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(AffectedUnitTextBox.Text))
            {
                MessageBox.Show("請填寫影響單位", "驗證失敗", MessageBoxButton.OK, MessageBoxImage.Warning);
                AffectedUnitTextBox.Focus();
                return false;
            }

            if (StartTimeComboBox.SelectedItem == null)
            {
                MessageBox.Show("請選擇施工時間", "驗證失敗", MessageBoxButton.OK, MessageBoxImage.Warning);
                StartTimeComboBox.Focus();
                return false;
            }

            if (EndTimeComboBox.SelectedItem == null)
            {
                MessageBox.Show("請選擇結束時間", "驗證失敗", MessageBoxButton.OK, MessageBoxImage.Warning);
                EndTimeComboBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(AffectedItemsTextBox.Text))
            {
                MessageBox.Show("請填寫影響項目", "驗證失敗", MessageBoxButton.OK, MessageBoxImage.Warning);
                AffectedItemsTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(ReasonTextBox.Text))
            {
                MessageBox.Show("請填寫事由", "驗證失敗", MessageBoxButton.OK, MessageBoxImage.Warning);
                ReasonTextBox.Focus();
                return false;
            }

            return true;
        }

        private void GenerateExcelFile(MaintenanceNotification notification)
        {
            var fileName = $"維護通告-{DateTime.Now:yyyy-MM}.xlsx";
            var filePath = Path.Combine(Environment.CurrentDirectory, fileName);

            using (var package = new ExcelPackage())
            {
                ExcelWorksheet worksheet;
                int nextRow = 1;

                // 檢查文件是否已存在
                if (File.Exists(filePath))
                {
                    // 載入現有文件並追加數據
                    using (var existingPackage = new ExcelPackage(new FileInfo(filePath)))
                    {
                        worksheet = existingPackage.Workbook.Worksheets[0];
                        nextRow = worksheet.Dimension?.End.Row + 1 ?? 2;
                        
                        // 添加新記錄到現有文件
                        var currentSerialNumber = nextRow - 1;
                        worksheet.Cells[nextRow, 1].Value = currentSerialNumber;
                        worksheet.Cells[nextRow, 2].Value = notification.Type;
                        worksheet.Cells[nextRow, 3].Value = notification.Date;
                        worksheet.Cells[nextRow, 4].Value = notification.NotifyUnit;
                        worksheet.Cells[nextRow, 5].Value = notification.ConstructionUnit;
                        worksheet.Cells[nextRow, 6].Value = notification.AffectedUnit;
                        worksheet.Cells[nextRow, 7].Value = $"{notification.StartTime}-{notification.EndTime}";
                        worksheet.Cells[nextRow, 8].Value = notification.AffectedItems;
                        worksheet.Cells[nextRow, 9].Value = notification.Reason;
                        worksheet.Cells[nextRow, 10].Value = notification.GenerateEmail ? "是" : "否";
                        worksheet.Cells[nextRow, 11].Value = notification.NotificationTime.ToString("yyyy-MM-dd HH:mm:ss");

                        // 自動調整列寬
                        worksheet.Cells.AutoFitColumns();

                        // 保存文件
                        existingPackage.Save();
                    }
                    return; // 直接返回，不需要創建新package
                }
                else
                {
                    // 創建新文件
                    worksheet = package.Workbook.Worksheets.Add("維護通告記錄");
                    
                    // 設定標題
                    worksheet.Cells[1, 1].Value = "序號";
                    worksheet.Cells[1, 2].Value = "類型";
                    worksheet.Cells[1, 3].Value = "日期";
                    worksheet.Cells[1, 4].Value = "通知單位";
                    worksheet.Cells[1, 5].Value = "施工單位";
                    worksheet.Cells[1, 6].Value = "影響單位";
                    worksheet.Cells[1, 7].Value = "施工時間";
                    worksheet.Cells[1, 8].Value = "影響項目";
                    worksheet.Cells[1, 9].Value = "事由";
                    worksheet.Cells[1, 10].Value = "產出信件";
                    worksheet.Cells[1, 11].Value = "通告時間";
                    
                    // 格式化標題
                    using (var range = worksheet.Cells[1, 1, 1, 11])
                    {
                        range.Style.Font.Bold = true;
                        range.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    }
                    
                    nextRow = 2;
                }

                // 添加新記錄
                var serialNumber = nextRow - 1;
                worksheet.Cells[nextRow, 1].Value = serialNumber;
                worksheet.Cells[nextRow, 2].Value = notification.Type;
                worksheet.Cells[nextRow, 3].Value = notification.Date;
                worksheet.Cells[nextRow, 4].Value = notification.NotifyUnit;
                worksheet.Cells[nextRow, 5].Value = notification.ConstructionUnit;
                worksheet.Cells[nextRow, 6].Value = notification.AffectedUnit;
                worksheet.Cells[nextRow, 7].Value = $"{notification.StartTime}-{notification.EndTime}";
                worksheet.Cells[nextRow, 8].Value = notification.AffectedItems;
                worksheet.Cells[nextRow, 9].Value = notification.Reason;
                worksheet.Cells[nextRow, 10].Value = notification.GenerateEmail ? "是" : "否";
                worksheet.Cells[nextRow, 11].Value = notification.NotificationTime.ToString("yyyy-MM-dd HH:mm:ss");

                // 自動調整列寬
                worksheet.Cells.AutoFitColumns();

                // 保存文件
                package.SaveAs(new FileInfo(filePath));
            }

            System.Diagnostics.Debug.WriteLine($"Excel文件已生成: {fileName}");
        }

        private void AddToWorkRecord(MaintenanceNotification notification)
        {
            try
            {
                // 格式：通告 計畫性 影響單位 mmdd（六） 施工單位事由
                var weekdays = new[] { "日", "一", "二", "三", "四", "五", "六" };
                var weekday = weekdays[(int)notification.SelectedDate.DayOfWeek];
                var workRecord = $"通告 {notification.Type} {notification.AffectedUnit} {notification.Date}（{weekday}） {notification.ConstructionUnit}{notification.Reason}";

                // 調用MainWindow的LogWorkTask方法
                if (mainWindow != null)
                {
                    mainWindow.LogWorkTaskFromExternal(workRecord);
                }

                System.Diagnostics.Debug.WriteLine($"工作記錄已添加: {workRecord}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"添加工作記錄失敗: {ex.Message}");
            }
        }

        private void ClearForm()
        {
            TypeComboBox.SelectedIndex = 1; // 預設為計劃性
            DatePicker.SelectedDate = DateTime.Now.AddDays(1); // 明天日期
            NotifyUnitTextBox.Clear();
            ConstructionUnitTextBox.Clear();
            AffectedUnitTextBox.Clear();
            StartTimeComboBox.SelectedItem = "17:30";
            EndTimeComboBox.SelectedItem = "23:30";
            AffectedItemsTextBox.Clear();
            ReasonTextBox.Clear();
            GenerateEmailCheckBox.IsChecked = true;

            // 設定焦點到第一個欄位
            NotifyUnitTextBox.Focus();
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }

        private void GenerateOutlookEmail(MaintenanceNotification notification)
        {
            try
            {
                var templatePath = "範本_維護通告.txt";
                if (!File.Exists(templatePath))
                {
                    MessageBox.Show($"找不到範本文件: {templatePath}", "警告", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 讀取範本文件
                var templateContent = File.ReadAllText(templatePath, System.Text.Encoding.UTF8);

                // 解析範本
                var emailData = ParseTemplate(templateContent, notification);

                // 創建Outlook信件
                CreateOutlookEmail(emailData);

                System.Diagnostics.Debug.WriteLine("Outlook信件已生成");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"生成Outlook信件失敗：{ex.Message}", "錯誤", MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"生成Outlook信件失敗: {ex.Message}");
            }
        }

        private EmailData ParseTemplate(string templateContent, MaintenanceNotification notification)
        {
            var emailData = new EmailData();
            var lines = templateContent.Split('\n');

            foreach (var line in lines)
            {
                var trimmedLine = line.Trim();

                if (trimmedLine.StartsWith("*收件人："))
                {
                    emailData.Recipients = trimmedLine.Substring(4);
                }
                else if (trimmedLine.StartsWith("*副本收件人："))
                {
                    emailData.CcRecipients = trimmedLine.Substring(6);
                }
                else if (trimmedLine.StartsWith("標題："))
                {
                    emailData.Subject = trimmedLine.Substring(3);
                }
                else if (trimmedLine == "內文：")
                {
                    // 開始收集內文
                    var contentLines = new List<string>();
                    for (int i = Array.IndexOf(lines, line) + 1; i < lines.Length; i++)
                    {
                        contentLines.Add(lines[i]);
                    }
                    emailData.Body = string.Join("\n", contentLines);
                    break;
                }
            }

            // 替換變數
            emailData = ReplaceVariables(emailData, notification);

            return emailData;
        }

        private EmailData ReplaceVariables(EmailData emailData, MaintenanceNotification notification)
        {
            // 處理日期格式
            var formattedDate = FormatDateFromDateTime(notification.SelectedDate);
            var constructionTime = $"{notification.StartTime}-{notification.EndTime}";

            // 替換標題變數
            emailData.Subject = emailData.Subject
                .Replace("（日期）", formattedDate)
                .Replace("（日期1）", formattedDate)
                .Replace("（影響單位）", notification.AffectedUnit)
                .Replace("（影響項目）", notification.AffectedItems);

            // 替換內文變數
            emailData.Body = emailData.Body
                .Replace("（通知單位）", notification.NotifyUnit)
                .Replace("（施工單位）", notification.ConstructionUnit)
                .Replace("（事由）", notification.Reason)
                .Replace("（日期1）", formattedDate)
                .Replace("（施工時間1）", constructionTime)
                .Replace("（影響單位）", notification.AffectedUnit)
                .Replace("（影響項目）", notification.AffectedItems);

            // 處理多筆日期邏輯：如僅一筆則刪除日期2與日期3這二行
            if (emailData.Body.Contains("（日期2）") && emailData.Body.Contains("（日期3）"))
            {
                // 檢查是否有多筆維護作業（這裡假設只有一筆，所以刪除日期2和日期3行）
                var lines = emailData.Body.Split('\n').ToList();

                // 移除包含（日期2）和（日期3）的行
                for (int i = lines.Count - 1; i >= 0; i--)
                {
                    if (lines[i].Contains("（日期2）") || lines[i].Contains("（日期3）"))
                    {
                        lines.RemoveAt(i);
                    }
                }

                emailData.Body = string.Join("\n", lines);
            }

            return emailData;
        }

        private string FormatDate(string originalDate)
        {
            try
            {
                // mmdd格式處理
                if (originalDate.Length == 4)
                {
                    var month = originalDate.Substring(0, 2);
                    var day = originalDate.Substring(2, 2);

                    // 計算星期幾
                    var currentYear = DateTime.Now.Year;
                    var dateObj = new DateTime(currentYear, int.Parse(month), int.Parse(day));
                    var weekdays = new[] { "一", "二", "三", "四", "五", "六", "日" };
                    var weekday = weekdays[(int)dateObj.DayOfWeek];

                    return $"{month}/{day}({weekday})";
                }

                return originalDate;
            }
            catch
            {
                return originalDate;
            }
        }

        private string FormatDateFromDateTime(DateTime date)
        {
            try
            {
                var weekdays = new[] { "日", "一", "二", "三", "四", "五", "六" };
                var weekday = weekdays[(int)date.DayOfWeek];
                return $"{date.Month:D2}/{date.Day:D2}({weekday})";
            }
            catch
            {
                return date.ToString("MM/dd");
            }
        }

        private void CreateOutlookEmail(EmailData emailData)
        {
            try
            {
                // 使用COM創建Outlook應用程式
                dynamic outlook = Activator.CreateInstance(Type.GetTypeFromProgID("Outlook.Application"));
                dynamic mailItem = outlook.CreateItem(0); // 0 = olMailItem

                // 設定收件人
                if (!string.IsNullOrEmpty(emailData.Recipients))
                {
                    mailItem.To = emailData.Recipients;
                }

                // 設定副本收件人
                if (!string.IsNullOrEmpty(emailData.CcRecipients))
                {
                    mailItem.CC = emailData.CcRecipients;
                }

                // 設定標題和內文
                mailItem.Subject = emailData.Subject;
                mailItem.Body = emailData.Body;

                // 顯示信件（不自動發送）
                mailItem.Display();

                System.Diagnostics.Debug.WriteLine("Outlook信件草稿已開啟");
            }
            catch (Exception ex)
            {
                throw new Exception($"無法創建Outlook信件：{ex.Message}");
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    // 維護通告記錄類別
    public class MaintenanceNotification
    {
        public string Type { get; set; }
        public string Date { get; set; }
        public string NotifyUnit { get; set; }
        public string ConstructionUnit { get; set; }
        public string AffectedUnit { get; set; }
        public string StartTime { get; set; }
        public string EndTime { get; set; }
        public string AffectedItems { get; set; }
        public string Reason { get; set; }
        public bool GenerateEmail { get; set; }
        public DateTime NotificationTime { get; set; }
        public DateTime SelectedDate { get; set; }
    }

    // 信件數據類別
    public class EmailData
    {
        public string Recipients { get; set; } = "";
        public string CcRecipients { get; set; } = "";
        public string Subject { get; set; } = "";
        public string Body { get; set; } = "";
    }
}
