<Window x:Class="DesktopAssistantWidget.FunctionMenu"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="功能選單" Height="320" Width="Auto"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        Topmost="True"
        ResizeMode="NoResize"
        ShowInTaskbar="False"
        SizeToContent="Width">
    <Border Background="#F0F8FF" BorderBrush="#4682B4" BorderThickness="2" CornerRadius="10" Opacity="0.95">
        <Grid Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 第一排按鈕 -->
            <StackPanel x:Name="FirstRowPanel" Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,5"/>

            <!-- 第二排按鈕 -->
            <StackPanel x:Name="SecondRowPanel" Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,5"/>

            <!-- 第三排按鈕 -->
            <StackPanel x:Name="ThirdRowPanel" Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,5"/>
        </Grid>
    </Border>
</Window>
