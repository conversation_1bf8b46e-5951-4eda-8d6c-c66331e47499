# 🚨 程式鎖定問題解決方案

## 問題說明
程式「桌面助手小工具」正在運行中，導致無法重新編譯。

## 🔧 解決步驟

### 步驟 1: 停止正在運行的程式

#### 方法 A: 使用工作管理員 (推薦)
1. 按 `Ctrl + Shift + Esc` 開啟工作管理員
2. 在「處理程序」分頁中找到：
   - `DesktopAssistantWidget.exe`
   - 或「桌面助手小工具」
3. 右鍵點擊 → 選擇「結束工作」
4. 確認結束程序

#### 方法 B: 使用命令列
開啟命令提示字元，執行：
```cmd
taskkill /F /IM DesktopAssistantWidget.exe
```

#### 方法 C: 直接關閉程式
如果程式視窗還可見：
- 右鍵點擊角色圖片
- 選擇退出或關閉選項
- 或按 Alt + F4

### 步驟 2: 清理並重新編譯

在專案目錄中執行：
```cmd
dotnet clean
dotnet build
```

### 步驟 3: 運行程式
```cmd
dotnet run
```

## 🔍 如何確認程式已停止

### 檢查方法 1: 工作管理員
- 在工作管理員中確認沒有 `DesktopAssistantWidget.exe` 程序

### 檢查方法 2: 命令列
```cmd
tasklist | findstr DesktopAssistant
```
如果沒有輸出，表示程式已停止

### 檢查方法 3: 視覺確認
- 桌面上的角色圖片應該消失

## 🚀 完整解決流程

```cmd
# 1. 停止程式 (如果命令列方法可用)
taskkill /F /IM DesktopAssistantWidget.exe

# 2. 清理專案
dotnet clean

# 3. 重新編譯
dotnet build

# 4. 運行程式
dotnet run
```

## 💡 預防措施

### 開發時的建議
1. **使用 Visual Studio 的停止按鈕**
   - 在 Visual Studio 中按 Shift + F5 停止偵錯

2. **添加退出功能**
   - 在右鍵選單中添加「退出」選項
   - 使用快捷鍵 (如 Ctrl + Q) 退出程式

3. **使用偵錯模式**
   - 在 Visual Studio 中使用 F5 啟動偵錯
   - 這樣可以更容易停止程式

## 🔄 如果問題持續

### 重啟電腦
如果程式無法正常停止：
1. 儲存所有工作
2. 重新啟動電腦
3. 重新開啟專案並編譯

### 檢查防毒軟體
某些防毒軟體可能會鎖定檔案：
1. 暫時停用即時保護
2. 將專案資料夾加入白名單
3. 重新編譯

### 檢查檔案權限
確保您有足夠的權限：
1. 以系統管理員身分開啟命令提示字元
2. 執行編譯命令

## ✅ 成功指標

編譯成功後，您應該看到：
```
建置成功
  0 個警告
  0 個錯誤
```

然後就可以正常運行程式了！

---

**注意**: 這是開發過程中的常見問題，停止程式後重新編譯即可解決。
