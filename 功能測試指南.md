# 🧪 功能測試指南

## 📋 測試清單

### ✅ 已確認正常運行的功能

#### 1. 聊天記錄系統 ✅
- [x] 程式啟動時自動創建 `chat_logs` 資料夾
- [x] 自動創建今日對話記錄檔案：`2025-06-29.txt`
- [x] 自動創建今日工作記錄檔案：`工作記錄-2025-06-29.csv`
- [x] 檔案包含正確的標題和格式

#### 2. 程式基本功能 ✅
- [x] 程式正常啟動
- [x] 角色圖片高度調整為 250 像素
- [x] 待機動畫正常播放
- [x] 編譯無錯誤

## 🔬 需要測試的功能

### 1. 對話記錄功能
**測試步驟**：
1. 雙擊角色圖片開啟輸入框
2. 輸入一般對話內容，例如：「你好」
3. 確認輸入並查看回覆
4. 檢查 `chat_logs/2025-06-29.txt` 是否記錄了對話

**預期結果**：
```
=== 對話記錄 2025-06-29 ===
[2025-06-29 14:30:15] 使用者: 你好
[2025-06-29 14:30:15] 助手: 收到了！
```

### 2. 工作記錄功能
**測試步驟**：
1. 雙擊角色圖片開啟輸入框
2. 輸入包含任務關鍵字的內容，例如：「提醒我明天開會」
3. 確認輸入並查看回覆
4. 檢查 `chat_logs/工作記錄-2025-06-29.csv` 是否記錄了任務

**預期結果**：
```
時間戳記,輸入內容,完成時間戳記
2025-06-29 14:30:15,提醒我明天開會,
```

### 3. 滑鼠事件測試
**測試步驟**：
1. **雙擊測試**：快速雙擊角色圖片，應該開啟輸入框
2. **右鍵測試**：右鍵點擊角色圖片，應該開啟功能選單
3. **拖拽測試**：按住左鍵拖拽角色，應該顯示拖拽動畫

**預期結果**：
- 雙擊響應靈敏，不會與拖拽衝突
- 右鍵選單正常出現在角色中間
- 拖拽功能正常，有方向動畫

### 4. 程式結束功能
**測試步驟**：
1. 右鍵點擊角色開啟功能選單
2. 點擊「結束程式」按鈕
3. 在確認對話框中選擇「是」

**預期結果**：
- 顯示確認對話框
- 選擇「是」後程式完全關閉
- 所有視窗都正確關閉

### 5. 任務關鍵字識別測試
**測試不同關鍵字**：
- 「提醒我...」
- 「記住...」
- 「待辦事項...」
- 「任務...」
- 「要做...」
- 「完成...」
- 「處理...」
- 「安排...」
- 「工...」

**預期結果**：
包含這些關鍵字的輸入應該同時記錄到對話記錄和工作記錄中。

## 🐛 可能遇到的問題

### 問題 1: 雙擊沒反應
**可能原因**：點擊太快或太慢
**解決方法**：調整點擊間隔，在 50-500ms 之間

### 問題 2: 右鍵選單沒出現
**可能原因**：事件處理衝突
**解決方法**：確保右鍵點擊在角色圖片上

### 問題 3: 記錄檔案沒有內容
**可能原因**：檔案權限或路徑問題
**解決方法**：檢查程式是否有寫入權限

### 問題 4: 程式無法結束
**可能原因**：視窗關閉邏輯問題
**解決方法**：使用工作管理員強制結束，然後重新啟動

## 📊 測試記錄表

| 功能 | 狀態 | 測試時間 | 備註 |
|------|------|----------|------|
| 程式啟動 | ✅ | 已測試 | 正常 |
| 聊天記錄系統初始化 | ✅ | 已測試 | 檔案正確創建 |
| 雙擊輸入框 | ⏳ | 待測試 | |
| 右鍵功能選單 | ⏳ | 待測試 | |
| 對話記錄 | ⏳ | 待測試 | |
| 工作記錄 | ⏳ | 待測試 | |
| 任務關鍵字識別 | ⏳ | 待測試 | |
| 程式結束功能 | ⏳ | 待測試 | |
| 拖拽動畫 | ⏳ | 待測試 | |
| 前日任務載入 | ⏳ | 待測試 | 需要隔日測試 |

## 🔧 Debug 資訊查看

如果在 Visual Studio 中運行，可以在「輸出」視窗的「偵錯」分頁中查看詳細資訊：

```
聊天記錄系統初始化完成
對話記錄: chat_logs/2025-06-29.txt
工作記錄: chat_logs/工作記錄-2025-06-29.csv
記錄對話: [使用者輸入內容]
記錄工作任務: [任務內容]
```

## 📝 測試建議

1. **逐項測試**：按照清單逐一測試每個功能
2. **記錄結果**：在測試記錄表中標記結果
3. **檢查檔案**：每次測試後檢查相關記錄檔案
4. **多次測試**：重要功能建議測試多次確保穩定性
5. **異常處理**：嘗試一些異常操作，測試程式穩定性

## 🎯 測試完成標準

所有功能測試通過後，應該能夠：
- 正常進行對話並自動記錄
- 智能識別並記錄工作任務
- 流暢的滑鼠交互體驗
- 正確的程式結束流程
- 完整的記錄檔案管理

---

**開始測試吧！** 🚀
