using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Windows;

namespace DesktopAssistantWidget
{
    /// <summary>
    /// App.xaml 的互動邏輯
    /// </summary>
    public partial class App : Application
    {
        // Windows API 用於強制終止程序
        [DllImport("kernel32.dll", SetLastError = true)]
        static extern bool TerminateProcess(IntPtr hProcess, uint uExitCode);

        [DllImport("kernel32.dll", SetLastError = true)]
        static extern IntPtr GetCurrentProcess();
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // 設定應用程式關閉模式 - 當最後一個視窗關閉時就關閉應用程式
            this.ShutdownMode = ShutdownMode.OnLastWindowClose;

            // 設定應用程式的全域異常處理
            this.DispatcherUnhandledException += (sender, args) =>
            {
                MessageBox.Show($"發生錯誤：{args.Exception.Message}", "錯誤",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                args.Handled = true;
            };
        }

        // 添加強制關閉方法
        public static void ForceShutdown()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("App.ForceShutdown: 開始強制關閉應用程式");

                // 第一步：嘗試正常關閉所有視窗
                if (Application.Current != null)
                {
                    foreach (Window window in Application.Current.Windows)
                    {
                        try
                        {
                            System.Diagnostics.Debug.WriteLine($"App.ForceShutdown: 關閉視窗 {window.GetType().Name}");
                            window.Close();
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"App.ForceShutdown: 關閉視窗時發生錯誤: {ex.Message}");
                        }
                    }

                    // 第二步：嘗試應用程式關閉
                    try
                    {
                        Application.Current.Shutdown();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"App.ForceShutdown: Application.Shutdown 失敗: {ex.Message}");
                    }
                }

                // 第三步：使用 Environment.Exit
                System.Diagnostics.Debug.WriteLine("App.ForceShutdown: 使用 Environment.Exit(0)");
                System.Environment.Exit(0);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"App.ForceShutdown: 發生錯誤: {ex.Message}");

                // 最終手段：使用 Windows API 強制終止
                try
                {
                    System.Diagnostics.Debug.WriteLine("App.ForceShutdown: 使用 Windows API 強制終止");
                    TerminateProcess(GetCurrentProcess(), 0);
                }
                catch
                {
                    // 如果連 Windows API 都失敗，使用 Process.Kill
                    try
                    {
                        System.Diagnostics.Debug.WriteLine("App.ForceShutdown: 使用 Process.Kill");
                        Process.GetCurrentProcess().Kill();
                    }
                    catch
                    {
                        // 最後的最後
                        System.Environment.Exit(0);
                    }
                }
            }
        }
    }
}
