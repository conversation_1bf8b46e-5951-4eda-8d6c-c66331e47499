<Application x:Class="DesktopAssistantWidget.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <!-- 全域樣式和資源 -->
        <Style TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Microsoft JhengHei"/>
            <Setter Property="Foreground" Value="#333333"/>
        </Style>
        
        <Style TargetType="Button">
            <Setter Property="FontFamily" Value="Microsoft JhengHei"/>
            <Setter Property="Background" Value="#E8F4FD"/>
            <Setter Property="BorderBrush" Value="#B3D9FF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>
    </Application.Resources>
</Application>
