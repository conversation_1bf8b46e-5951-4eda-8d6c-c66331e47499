using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Imaging;
using System.Windows.Threading;
using System.Xml.Linq;

namespace DesktopAssistantWidget
{
    public partial class MainWindow : Window
    {
        // 動畫相關
        private DispatcherTimer animationTimer;
        private DispatcherTimer blinkTimer;
        private DispatcherTimer randomAnimationTimer;
        private DispatcherTimer autoReplyTimer;
        private DispatcherTimer dragCheckTimer;
        private Image characterImage;
        private int currentFrame = 0;

        // 動畫狀態
        private AnimationState currentAnimationState = AnimationState.Idle;
        private bool isDragging = false;
        private Point dragStartPosition;
        private Point lastMousePosition;

        // 圖片資源
        private List<BitmapImage> idleImages;        // 待機動畫 stand1~stand5
        private List<BitmapImage> dragImages;        // 拖拽動畫 drag
        private List<BitmapImage> dragLeftImages;    // 左拖動畫 drag_left
        private List<BitmapImage> dragRightImages;   // 右拖動畫 drag_right
        private List<BitmapImage> randomAnimation1;  // 隨機動畫1
        private List<BitmapImage> randomAnimation2;  // 隨機動畫2
        private List<BitmapImage> randomAnimation3;  // 隨機動畫3
        private BitmapImage blinkImage;              // 眨眼圖片

        // 其他
        private List<RssItem> rssItems;
        private int currentRssIndex = 0;
        private MessageWindow messageWindow;
        private TodoWindow todoWindow;

        // 聊天記錄和工作記錄
        private string chatLogPath;
        private string workLogPath;

        // 雙擊檢測
        private DateTime lastClickTime = DateTime.MinValue;

        // 動畫狀態枚舉
        private enum AnimationState
        {
            Idle,           // 待機
            Drag,           // 拖拽
            DragLeft,       // 左拖
            DragRight,      // 右拖
            Random1,        // 隨機動畫1
            Random2,        // 隨機動畫2
            Random3,        // 隨機動畫3
            Blink           // 眨眼
        }

        public MainWindow()
        {
            InitializeComponent();

            // 初始化聊天記錄系統
            InitializeChatLogs();

            // 簡單測試圖片資源
            System.Diagnostics.Debug.WriteLine("=== 開始測試圖片資源 ===");
            TestSimpleImageLoad();

            InitWidget();
            LoadCharacterImages();
            StartAnimationTimer();
            StartBlinkTimer();
            StartRandomAnimationTimer();
            StartAutoReplyTimer();
            InitializeSubWindows();
            _ = LoadRssNews(); // 非同步載入RSS新聞
        }

        private void InitializeChatLogs()
        {
            try
            {
                // 建立 chat_logs 資料夾
                string chatLogsDir = "chat_logs";
                if (!Directory.Exists(chatLogsDir))
                {
                    Directory.CreateDirectory(chatLogsDir);
                }

                // 設定今日檔案路徑
                string today = DateTime.Now.ToString("yyyy-MM-dd");
                chatLogPath = Path.Combine(chatLogsDir, $"{today}.txt");
                workLogPath = Path.Combine(chatLogsDir, $"工作記錄-{today}.csv");

                // 建立今日對話記錄檔案
                if (!File.Exists(chatLogPath))
                {
                    File.WriteAllText(chatLogPath, $"=== 對話記錄 {today} ===\n", System.Text.Encoding.UTF8);
                }

                // 建立今日工作記錄檔案
                if (!File.Exists(workLogPath))
                {
                    File.WriteAllText(workLogPath, "時間戳記,輸入內容,完成時間戳記\n", System.Text.Encoding.UTF8);

                    // 載入前日未完成事項
                    LoadPreviousDayUnfinishedTasks();
                }

                System.Diagnostics.Debug.WriteLine($"聊天記錄系統初始化完成");
                System.Diagnostics.Debug.WriteLine($"對話記錄: {chatLogPath}");
                System.Diagnostics.Debug.WriteLine($"工作記錄: {workLogPath}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"聊天記錄系統初始化失敗: {ex.Message}");
            }
        }

        private void LoadPreviousDayUnfinishedTasks()
        {
            try
            {
                // 尋找前一天的工作記錄檔案
                var yesterday = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd");
                var yesterdayWorkLog = Path.Combine("chat_logs", $"工作記錄-{yesterday}.csv");

                if (File.Exists(yesterdayWorkLog))
                {
                    var lines = File.ReadAllLines(yesterdayWorkLog, System.Text.Encoding.UTF8);
                    var unfinishedTasks = new List<string>();

                    // 跳過標題行，檢查未完成的任務
                    for (int i = 1; i < lines.Length; i++)
                    {
                        var parts = lines[i].Split(',');
                        if (parts.Length >= 2 && (parts.Length < 3 || string.IsNullOrWhiteSpace(parts[2])))
                        {
                            // 未完成的任務（沒有完成時間戳記）- 保持原建檔時間
                            unfinishedTasks.Add($"{parts[0]},{parts[1]},");
                        }
                    }

                    // 將未完成任務添加到今日檔案
                    if (unfinishedTasks.Count > 0)
                    {
                        File.AppendAllLines(workLogPath, unfinishedTasks, System.Text.Encoding.UTF8);
                        System.Diagnostics.Debug.WriteLine($"從前日載入 {unfinishedTasks.Count} 個未完成任務");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"載入前日未完成任務失敗: {ex.Message}");
            }
        }

        private void TestSimpleImageLoad()
        {
            try
            {
                // 測試載入 stand1.png
                var uri = new Uri("pack://application:,,,/Assets/stand1.png");
                var bitmap = new BitmapImage(uri);
                System.Diagnostics.Debug.WriteLine($"✅ stand1.png 載入成功: {bitmap.PixelWidth}x{bitmap.PixelHeight}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ stand1.png 載入失敗: {ex.Message}");

                // 嘗試列出所有資源
                var assembly = System.Reflection.Assembly.GetExecutingAssembly();
                var resources = assembly.GetManifestResourceNames();
                System.Diagnostics.Debug.WriteLine("可用資源:");
                foreach (var resource in resources)
                {
                    System.Diagnostics.Debug.WriteLine($"  - {resource}");
                }
            }
        }

        private void InitializeSubWindows()
        {
            // 建立子視窗但不顯示
            messageWindow = new MessageWindow();
            messageWindow.Hide();

            todoWindow = new TodoWindow();
            todoWindow.Hide();
        }

        private void InitWidget()
        {
            // 設定視窗屬性 - 160x250 像素
            this.Width = 160;
            this.Height = 250;
            this.WindowStyle = WindowStyle.None;
            this.AllowsTransparency = true;
            this.Background = Brushes.Transparent;
            this.Topmost = true;
            this.ResizeMode = ResizeMode.NoResize;

            // 建立角色圖片
            characterImage = new Image
            {
                Width = 160,
                Height = 250,
                Stretch = Stretch.Fill
            };

            // 設定滑鼠事件
            characterImage.MouseLeftButtonDown += OnCharacterMouseLeftButtonDown;
            characterImage.MouseLeftButtonUp += OnCharacterMouseLeftButtonUp;
            characterImage.MouseMove += OnCharacterMouseMove;
            characterImage.MouseRightButtonDown += OnCharacterMouseRightButtonDown;


            // 設定鍵盤事件
            this.KeyDown += OnMainWindowKeyDown;
            this.Focusable = true; // 讓視窗可以接收鍵盤事件

            // 設定視窗內容
            this.Content = characterImage;
        }

        private void LoadCharacterImages()
        {
            // 初始化所有圖片列表
            idleImages = new List<BitmapImage>();
            dragImages = new List<BitmapImage>();
            dragLeftImages = new List<BitmapImage>();
            dragRightImages = new List<BitmapImage>();
            randomAnimation1 = new List<BitmapImage>();
            randomAnimation2 = new List<BitmapImage>();
            randomAnimation3 = new List<BitmapImage>();

            // 載入待機動畫圖片 stand1.png ~ stand5.png
            System.Diagnostics.Debug.WriteLine("開始載入待機動畫圖片...");
            for (int i = 1; i <= 5; i++)
            {
                var image = LoadImage($"stand{i}.png");
                if (image != null)
                {
                    idleImages.Add(image);
                    System.Diagnostics.Debug.WriteLine($"成功載入 stand{i}.png");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"載入失敗 stand{i}.png");
                }
            }
            System.Diagnostics.Debug.WriteLine($"待機動畫圖片載入完成，共 {idleImages.Count} 張");

            // 載入拖拽相關圖片
            var dragImage = LoadImage("drag.png");
            if (dragImage != null)
                dragImages.Add(dragImage);

            var dragLeftImage = LoadImage("drag_left.png");
            if (dragLeftImage != null)
                dragLeftImages.Add(dragLeftImage);

            var dragRightImage = LoadImage("drag_right.png");
            if (dragRightImage != null)
                dragRightImages.Add(dragRightImage);

            // 載入眨眼圖片
            blinkImage = LoadImage("blink.png");

            // 載入隨機動畫1圖片 random1_1.png ~ random1_5.png
            System.Diagnostics.Debug.WriteLine("開始載入隨機動畫1圖片...");
            for (int i = 1; i <= 5; i++)
            {
                var image = LoadImage($"random1_{i}.png");
                if (image != null)
                {
                    randomAnimation1.Add(image);
                    System.Diagnostics.Debug.WriteLine($"成功載入 random1_{i}.png");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"載入失敗 random1_{i}.png");
                }
            }

            // 如果沒有找到 random1_*.png，嘗試使用現有的圖片作為隨機動畫1
            if (randomAnimation1.Count == 0)
            {
                System.Diagnostics.Debug.WriteLine("沒有找到 random1_*.png，嘗試使用現有圖片...");

                // 嘗試使用 icon1~icon6 作為隨機動畫1
                for (int i = 1; i <= 6; i++)
                {
                    var image = LoadImage($"icon{i}.png");
                    if (image != null)
                    {
                        randomAnimation1.Add(image);
                        System.Diagnostics.Debug.WriteLine($"使用 icon{i}.png 作為隨機動畫1");
                    }
                }

                // 如果還是沒有，使用待機動畫作為隨機動畫1
                if (randomAnimation1.Count == 0 && idleImages.Count > 0)
                {
                    randomAnimation1.AddRange(idleImages);
                    System.Diagnostics.Debug.WriteLine("使用待機動畫作為隨機動畫1");
                }
            }
            System.Diagnostics.Debug.WriteLine($"隨機動畫1圖片載入完成，共 {randomAnimation1.Count} 張");

            // 載入隨機動畫2 (如果有random2_1.png ~ random2_5.png)
            for (int i = 1; i <= 5; i++)
            {
                var image = LoadImage($"random2_{i}.png");
                if (image != null)
                    randomAnimation2.Add(image);
            }

            // 載入隨機動畫3 (如果有random3_1.png ~ random3_5.png)
            for (int i = 1; i <= 5; i++)
            {
                var image = LoadImage($"random3_{i}.png");
                if (image != null)
                    randomAnimation3.Add(image);
            }

            // 如果沒有專門的隨機動畫圖片，可以重複使用其他動畫
            if (randomAnimation2.Count == 0 && idleImages.Count > 0)
            {
                // 使用待機動畫的反向作為隨機動畫2
                for (int i = idleImages.Count - 1; i >= 0; i--)
                {
                    randomAnimation2.Add(idleImages[i]);
                }
                System.Diagnostics.Debug.WriteLine($"隨機動畫2使用待機動畫反向，共 {randomAnimation2.Count} 張");
            }

            if (randomAnimation3.Count == 0 && randomAnimation1.Count > 0)
            {
                // 使用隨機動畫1作為隨機動畫3的基礎
                randomAnimation3.AddRange(randomAnimation1);
                System.Diagnostics.Debug.WriteLine($"隨機動畫3使用隨機動畫1，共 {randomAnimation3.Count} 張");
            }

            // 設定初始圖片
            if (idleImages.Count > 0)
            {
                characterImage.Source = idleImages[0];
                System.Diagnostics.Debug.WriteLine("設定初始圖片為 stand1.png");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("警告：沒有載入到任何待機動畫圖片！");
                // 嘗試載入備用圖片
                var fallbackImage = LoadImage("character.png");
                if (fallbackImage != null)
                {
                    characterImage.Source = fallbackImage;
                    System.Diagnostics.Debug.WriteLine("使用備用圖片 character.png");
                }
            }
        }

        private BitmapImage LoadImage(string fileName)
        {
            // 嘗試多種路徑格式
            string[] possiblePaths = {
                $"pack://application:,,,/Assets/{fileName}",
                $"pack://application:,,,/{fileName}",
                $"/Assets/{fileName}",
                $"Assets/{fileName}",
                fileName
            };

            foreach (var path in possiblePaths)
            {
                try
                {
                    System.Diagnostics.Debug.WriteLine($"嘗試載入圖片路徑: {path}");

                    var uri = new Uri(path, path.StartsWith("pack://") ? UriKind.Absolute : UriKind.Relative);
                    var bitmap = new BitmapImage();
                    bitmap.BeginInit();
                    bitmap.UriSource = uri;
                    bitmap.CacheOption = BitmapCacheOption.OnLoad;
                    bitmap.EndInit();
                    bitmap.Freeze();

                    System.Diagnostics.Debug.WriteLine($"✅ 成功載入圖片: {fileName} (路徑: {path})");
                    return bitmap;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ 路徑失敗: {path}, 錯誤: {ex.Message}");
                }
            }

            // 如果所有路徑都失敗，嘗試從檔案系統載入
            try
            {
                var filePath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Assets", fileName);
                System.Diagnostics.Debug.WriteLine($"嘗試從檔案系統載入: {filePath}");

                if (System.IO.File.Exists(filePath))
                {
                    var bitmap = new BitmapImage();
                    bitmap.BeginInit();
                    bitmap.UriSource = new Uri(filePath, UriKind.Absolute);
                    bitmap.CacheOption = BitmapCacheOption.OnLoad;
                    bitmap.EndInit();
                    bitmap.Freeze();

                    System.Diagnostics.Debug.WriteLine($"✅ 從檔案系統成功載入: {fileName}");
                    return bitmap;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"❌ 檔案不存在: {filePath}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 檔案系統載入失敗: {fileName}, 錯誤: {ex.Message}");
            }

            System.Diagnostics.Debug.WriteLine($"❌ 所有方法都無法載入圖片: {fileName}");
            return null;
        }

        private void LogChatMessage(string userInput, string botReply = "")
        {
            try
            {
                var timestamp = DateTime.Now.ToString("HH:mm:ss");
                // 只記錄用戶輸入的訊息，時間格式不包含日期
                var logEntry = $"[{timestamp}] {userInput}\n";

                System.Diagnostics.Debug.WriteLine($"準備記錄對話到: {chatLogPath}");
                File.AppendAllText(chatLogPath, logEntry, System.Text.Encoding.UTF8);
                System.Diagnostics.Debug.WriteLine($"記錄對話成功: {userInput}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"記錄對話失敗: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"chatLogPath: {chatLogPath}");
            }
        }

        private void LogCalculationResult(string expression, double result)
        {
            try
            {
                var timestamp = DateTime.Now.ToString("HH:mm:ss");
                // 計算結果記錄格式：[時間] 表達式=結果
                var logEntry = $"[{timestamp}] {expression}={result}\n";

                System.Diagnostics.Debug.WriteLine($"準備記錄計算結果到: {chatLogPath}");
                File.AppendAllText(chatLogPath, logEntry, System.Text.Encoding.UTF8);
                System.Diagnostics.Debug.WriteLine($"記錄計算結果成功: {expression}={result}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"記錄計算結果失敗: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"chatLogPath: {chatLogPath}");
            }
        }

        private void LogWorkTask(string taskContent)
        {
            try
            {
                var timestamp = DateTime.Now.ToString("MM-dd HH:mm");
                var logEntry = $"{timestamp},{taskContent.Replace(",", "，")},\n";

                System.Diagnostics.Debug.WriteLine($"準備記錄工作任務到: {workLogPath}");
                File.AppendAllText(workLogPath, logEntry, System.Text.Encoding.UTF8);
                System.Diagnostics.Debug.WriteLine($"記錄工作任務成功: {taskContent}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"記錄工作任務失敗: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"workLogPath: {workLogPath}");
            }
        }

        public void LogWorkTaskFromExternal(string taskContent)
        {
            LogWorkTask(taskContent);
        }

        private void CompleteWorkTask(string taskContent)
        {
            try
            {
                // 讀取現有的工作記錄
                var lines = File.ReadAllLines(workLogPath, System.Text.Encoding.UTF8).ToList();
                var completionTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

                // 尋找並更新對應的任務
                for (int i = 1; i < lines.Count; i++) // 跳過標題行
                {
                    var parts = lines[i].Split(',');
                    if (parts.Length >= 2 && parts[1].Contains(taskContent) &&
                        (parts.Length < 3 || string.IsNullOrWhiteSpace(parts[2])))
                    {
                        // 找到未完成的任務，添加完成時間
                        lines[i] = $"{parts[0]},{parts[1]},{completionTime}";
                        break;
                    }
                }

                File.WriteAllLines(workLogPath, lines, System.Text.Encoding.UTF8);
                System.Diagnostics.Debug.WriteLine($"完成工作任務: {taskContent}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"完成工作任務失敗: {ex.Message}");
            }
        }

        private void StartAnimationTimer()
        {
            animationTimer = new DispatcherTimer();
            animationTimer.Tick += OnAnimationTick;
            animationTimer.Interval = TimeSpan.FromMilliseconds(300); // 每0.3秒一幀
            animationTimer.Start();
        }

        private void StartBlinkTimer()
        {
            blinkTimer = new DispatcherTimer();
            blinkTimer.Tick += OnBlinkTick;
            blinkTimer.Interval = TimeSpan.FromSeconds(6); // 每6秒眨眼一次
            blinkTimer.Start();
        }

        private void StartRandomAnimationTimer()
        {
            randomAnimationTimer = new DispatcherTimer();
            randomAnimationTimer.Tick += OnRandomAnimationTick;
            randomAnimationTimer.Interval = TimeSpan.FromSeconds(10); // 每10秒觸發隨機動畫
            randomAnimationTimer.Start();
        }

        private void OnAnimationTick(object sender, EventArgs e)
        {
            List<BitmapImage> currentImageList = GetCurrentImageList();

            if (currentImageList != null && currentImageList.Count > 0)
            {
                currentFrame = (currentFrame + 1) % currentImageList.Count;
                characterImage.Source = currentImageList[currentFrame];

                // 如果不是待機狀態且動畫播放完畢，回到待機狀態
                if (currentAnimationState != AnimationState.Idle &&
                    currentAnimationState != AnimationState.Drag &&
                    currentAnimationState != AnimationState.DragLeft &&
                    currentAnimationState != AnimationState.DragRight &&
                    currentFrame == 0)
                {
                    SetAnimationState(AnimationState.Idle);
                }
            }
        }

        private void OnBlinkTick(object sender, EventArgs e)
        {
            // 只在待機狀態且未拖拽時眨眼
            if (currentAnimationState == AnimationState.Idle && !isDragging && blinkImage != null)
            {
                // 記錄當前幀位置
                var savedFrame = currentFrame;
                var savedAnimationState = currentAnimationState;

                // 暫時顯示眨眼圖片
                characterImage.Source = blinkImage;

                // 0.3秒後恢復到正確的動畫幀
                var blinkRestoreTimer = new DispatcherTimer();
                blinkRestoreTimer.Interval = TimeSpan.FromMilliseconds(300);
                blinkRestoreTimer.Tick += (s, args) =>
                {
                    // 恢復到眨眼前的狀態和幀
                    currentAnimationState = savedAnimationState;
                    currentFrame = savedFrame;

                    var imageList = GetCurrentImageList();
                    if (imageList != null && imageList.Count > 0)
                    {
                        // 確保currentFrame在有效範圍內
                        if (currentFrame >= imageList.Count)
                            currentFrame = 0;
                        characterImage.Source = imageList[currentFrame];

                        System.Diagnostics.Debug.WriteLine($"眨眼恢復: 狀態={currentAnimationState}, 幀={currentFrame}");
                    }
                    blinkRestoreTimer.Stop();
                };
                blinkRestoreTimer.Start();
            }
        }

        private void OnRandomAnimationTick(object sender, EventArgs e)
        {
            // 只在待機狀態且未拖拽時播放隨機動畫
            if (currentAnimationState == AnimationState.Idle && !isDragging)
            {
                var random = new Random();
                var animationType = random.Next(1, 4); // 1-3的隨機數

                switch (animationType)
                {
                    case 1:
                        if (randomAnimation1.Count > 0)
                            SetAnimationState(AnimationState.Random1);
                        break;
                    case 2:
                        if (randomAnimation2.Count > 0)
                            SetAnimationState(AnimationState.Random2);
                        break;
                    case 3:
                        if (randomAnimation3.Count > 0)
                            SetAnimationState(AnimationState.Random3);
                        break;
                }
            }
        }

        private List<BitmapImage> GetCurrentImageList()
        {
            return currentAnimationState switch
            {
                AnimationState.Idle => idleImages,
                AnimationState.Drag => dragImages,
                AnimationState.DragLeft => dragLeftImages,
                AnimationState.DragRight => dragRightImages,
                AnimationState.Random1 => randomAnimation1,
                AnimationState.Random2 => randomAnimation2,
                AnimationState.Random3 => randomAnimation3,
                _ => idleImages
            };
        }

        private void SetAnimationState(AnimationState newState)
        {
            if (currentAnimationState != newState)
            {
                System.Diagnostics.Debug.WriteLine($"動畫狀態變更: {currentAnimationState} -> {newState}");
                currentAnimationState = newState;
                currentFrame = 0; // 重置動畫幀

                // 立即更新第一幀
                var imageList = GetCurrentImageList();
                if (imageList != null && imageList.Count > 0)
                {
                    characterImage.Source = imageList[0];
                    System.Diagnostics.Debug.WriteLine($"設定動畫圖片: {newState}, 圖片數量: {imageList.Count}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"警告: {newState} 狀態沒有可用圖片");
                }
            }
        }

        private void StartAutoReplyTimer()
        {
            autoReplyTimer = new DispatcherTimer();
            autoReplyTimer.Interval = TimeSpan.FromSeconds(300); // 每300秒
            autoReplyTimer.Tick += OnAutoReplyTick;
            autoReplyTimer.Start();
        }

        private async void OnAutoReplyTick(object sender, EventArgs e)
        {
            // 隨機選擇顯示一般回覆或RSS新聞
            var random = new Random();
            if (random.Next(2) == 0)
            {
                // 顯示一般回覆
                ShowReplyWindow(GetRandomReply());
            }
            else
            {
                // 顯示RSS新聞
                await ShowRandomRssNews();
            }
        }

        public async Task ShowRandomRssNews()
        {
            if (rssItems == null || rssItems.Count == 0)
            {
                await LoadRssNews();
            }

            if (rssItems != null && rssItems.Count > 0)
            {
                currentRssIndex = (currentRssIndex + 1) % rssItems.Count;
                var rssItem = rssItems[currentRssIndex];

                // 顯示新聞標題和隨機回應對話，格式：新聞標題在前，回應在後
                var newsReply = GetNewsReply();
                ShowReplyWindow($"{rssItem.Title}\n{newsReply}", true, rssItem.Link);

                System.Diagnostics.Debug.WriteLine($"顯示RSS新聞: {rssItem.Title}");
            }
        }

        private async Task LoadRssNews()
        {
            try
            {
                using var client = new HttpClient();
                client.Timeout = TimeSpan.FromSeconds(15);

                // 設定User-Agent避免被封鎖
                client.DefaultRequestHeaders.Add("User-Agent",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");

                // 忽略SSL證書錯誤（管理者權限可能會有SSL問題）
                var handler = new HttpClientHandler()
                {
                    ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true
                };

                using var secureClient = new HttpClient(handler);
                secureClient.Timeout = TimeSpan.FromSeconds(15);
                secureClient.DefaultRequestHeaders.Add("User-Agent",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");

                string rssUrl = "https://www.ithome.com.tw/rss";
                string rssContent = await secureClient.GetStringAsync(rssUrl);

                var doc = XDocument.Parse(rssContent);
                rssItems = doc.Descendants("item")
                    .Take(10) // 只取前10則新聞
                    .Select(item => new RssItem
                    {
                        Title = item.Element("title")?.Value ?? "無標題",
                        Link = item.Element("link")?.Value ?? "",
                        Description = item.Element("description")?.Value ?? ""
                    })
                    .ToList();

                System.Diagnostics.Debug.WriteLine($"RSS新聞載入成功，共 {rssItems.Count} 則");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"RSS載入失敗: {ex.Message}");

                // RSS載入失敗時使用預設訊息
                rssItems = new List<RssItem>
                {
                    new RssItem { Title = "無法載入新聞", Link = "", Description = "網路連線異常或權限問題" }
                };
            }
        }

        private void ShowReplyWindow(string message, bool isLink = false, string linkUrl = "")
        {
            var replyWindow = new ReplyWindow(message, isLink, linkUrl);

            // 等待視窗載入完成後設定位置
            replyWindow.Loaded += (s, e) =>
            {
                // 使用主視窗位置計算角色上方位置
                var characterCenterX = this.Left + this.Width / 2;
                var characterTopY = this.Top;

                // 將回覆視窗置中於角色上方
                var windowLeft = characterCenterX - replyWindow.ActualWidth / 2;
                var windowTop = characterTopY - replyWindow.ActualHeight - 10; // 上方10像素間距

                // 檢查螢幕邊界
                var workingArea = SystemParameters.WorkArea;
                if (windowLeft < workingArea.Left)
                    windowLeft = workingArea.Left + 10;
                if (windowLeft + replyWindow.ActualWidth > workingArea.Right)
                    windowLeft = workingArea.Right - replyWindow.ActualWidth - 10;
                if (windowTop < workingArea.Top)
                    windowTop = this.Top + this.Height + 10; // 如果上方空間不足，顯示在下方

                replyWindow.Left = windowLeft;
                replyWindow.Top = windowTop;

                System.Diagnostics.Debug.WriteLine($"回覆視窗位置: 角色({characterCenterX}, {characterTopY}) 回覆框({windowLeft}, {windowTop})");
            };

            replyWindow.Show();
        }

        #region 滑鼠事件處理

        private void OnCharacterMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            var now = DateTime.Now;
            var timeSinceLastClick = (now - lastClickTime).TotalMilliseconds;

            System.Diagnostics.Debug.WriteLine($"滑鼠左鍵按下，距離上次點擊: {timeSinceLastClick}ms");

            // 雙擊檢測 (100-400ms之間)
            if (lastClickTime != DateTime.MinValue && timeSinceLastClick > 100 && timeSinceLastClick < 400)
            {
                System.Diagnostics.Debug.WriteLine("檢測到雙擊，準備開啟輸入框");
                e.Handled = true;

                // 使用Dispatcher確保在UI線程中執行
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    System.Diagnostics.Debug.WriteLine("開始執行ShowInputDialog");
                    ShowInputDialog();
                }));

                lastClickTime = DateTime.MinValue;
                return;
            }

            lastClickTime = now;

            // 立即開始拖拽
            System.Diagnostics.Debug.WriteLine("開始拖拽");
            StartDragging();
        }

        private void StartDragging()
        {
            // 記錄拖拽開始位置（視窗位置和滑鼠位置）
            dragStartPosition = new Point(this.Left, this.Top);
            lastMousePosition = Mouse.GetPosition(null); // 使用螢幕座標
            isDragging = true;
            SetAnimationState(AnimationState.Drag);

            System.Diagnostics.Debug.WriteLine($"開始拖拽，起始位置: ({dragStartPosition.X}, {dragStartPosition.Y}), 滑鼠螢幕位置: ({lastMousePosition.X}, {lastMousePosition.Y})");

            // 開始拖拽檢查計時器（更高頻率）
            StartDragCheckTimer();

            // 立即開始拖拽
            try
            {
                this.DragMove();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"拖拽異常: {ex.Message}");
            }
            finally
            {
                // 拖拽結束
                StopDragCheckTimer();
                OnDragEnd();
            }
        }



        private void OnDragEnd()
        {
            System.Diagnostics.Debug.WriteLine("拖拽結束，檢查邊界並回到待機狀態");
            isDragging = false;

            // 檢查並修正角色位置，確保不超出螢幕範圍
            CheckAndFixWindowPosition();

            // 延遲一點時間再回到待機狀態，讓方向動畫能顯示一下
            var endTimer = new DispatcherTimer();
            endTimer.Interval = TimeSpan.FromMilliseconds(200);
            endTimer.Tick += (s, args) =>
            {
                SetAnimationState(AnimationState.Idle);
                endTimer.Stop();
            };
            endTimer.Start();
        }

        private void CheckAndFixWindowPosition()
        {
            var workingArea = SystemParameters.WorkArea;
            var newLeft = this.Left;
            var newTop = this.Top;

            // 檢查水平邊界
            if (this.Left < workingArea.Left)
                newLeft = workingArea.Left;
            else if (this.Left + this.Width > workingArea.Right)
                newLeft = workingArea.Right - this.Width;

            // 檢查垂直邊界
            if (this.Top < workingArea.Top)
                newTop = workingArea.Top;
            else if (this.Top + this.Height > workingArea.Bottom)
                newTop = workingArea.Bottom - this.Height;

            // 如果位置需要調整，移動視窗
            if (newLeft != this.Left || newTop != this.Top)
            {
                this.Left = newLeft;
                this.Top = newTop;
                System.Diagnostics.Debug.WriteLine($"角色位置已修正到: ({newLeft}, {newTop})");
            }
        }

        private void StartDragCheckTimer()
        {
            // 確保先停止任何現有的計時器
            StopDragCheckTimer();

            dragCheckTimer = new DispatcherTimer();
            dragCheckTimer.Interval = TimeSpan.FromMilliseconds(16); // 每16ms檢查一次（約60FPS）
            dragCheckTimer.Tick += OnDragCheckTick;
            dragCheckTimer.Start();

            System.Diagnostics.Debug.WriteLine("拖拽檢查計時器已啟動（高頻率模式）");
        }

        private void StopDragCheckTimer()
        {
            if (dragCheckTimer != null)
            {
                dragCheckTimer.Stop();
                dragCheckTimer.Tick -= OnDragCheckTick; // 移除事件處理器
                dragCheckTimer = null;
                System.Diagnostics.Debug.WriteLine("拖拽檢查計時器已停止");
            }
        }

        private void OnDragCheckTick(object sender, EventArgs e)
        {
            if (isDragging)
            {
                // 獲取當前滑鼠螢幕位置
                var currentMousePos = Mouse.GetPosition(null);

                // 計算滑鼠移動距離
                var deltaX = currentMousePos.X - lastMousePosition.X;

                // 根據滑鼠移動方向即時更新動畫（降低閾值提高靈敏度）
                if (Math.Abs(deltaX) > 1) // 滑鼠移動距離超過1像素就判斷方向
                {
                    if (deltaX > 0)
                    {
                        // 向右拖拽
                        if (currentAnimationState != AnimationState.DragRight)
                        {
                            SetAnimationState(AnimationState.DragRight);
                            System.Diagnostics.Debug.WriteLine($"即時切換到右拖動畫，滑鼠deltaX={deltaX}");
                        }
                    }
                    else
                    {
                        // 向左拖拽
                        if (currentAnimationState != AnimationState.DragLeft)
                        {
                            SetAnimationState(AnimationState.DragLeft);
                            System.Diagnostics.Debug.WriteLine($"即時切換到左拖動畫，滑鼠deltaX={deltaX}");
                        }
                    }

                    // 更新滑鼠位置記錄
                    lastMousePosition = currentMousePos;
                }
                // 移除else條件，讓方向動畫持續顯示
            }
        }

        private void OnCharacterMouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (isDragging)
            {
                System.Diagnostics.Debug.WriteLine("拖拽結束");
                OnDragEnd();
            }
        }

        private void OnCharacterMouseMove(object sender, MouseEventArgs e)
        {
            // 這個方法提供即時響應，與定時器配合工作
            if (isDragging)
            {
                // 獲取當前滑鼠螢幕位置
                var currentMousePos = Mouse.GetPosition(null);

                // 計算滑鼠移動距離
                var deltaX = currentMousePos.X - lastMousePosition.X;

                System.Diagnostics.Debug.WriteLine($"即時滑鼠移動檢測: deltaX={deltaX}, 螢幕位置=({currentMousePos.X}, {currentMousePos.Y})");

                // 根據滑鼠移動方向即時更新動畫
                if (Math.Abs(deltaX) > 0.5) // 極低閾值，幾乎任何移動都會觸發
                {
                    if (deltaX > 0)
                    {
                        // 向右拖拽
                        if (currentAnimationState != AnimationState.DragRight)
                        {
                            SetAnimationState(AnimationState.DragRight);
                            System.Diagnostics.Debug.WriteLine("即時滑鼠移動：切換到右拖動畫");
                        }
                    }
                    else
                    {
                        // 向左拖拽
                        if (currentAnimationState != AnimationState.DragLeft)
                        {
                            SetAnimationState(AnimationState.DragLeft);
                            System.Diagnostics.Debug.WriteLine("即時滑鼠移動：切換到左拖動畫");
                        }
                    }

                    // 更新滑鼠位置記錄
                    lastMousePosition = currentMousePos;
                }
            }
        }

        private void OnCharacterMouseRightButtonDown(object sender, MouseButtonEventArgs e)
        {
            // 右鍵點擊 - 開啟功能選單，位置設在角色圖片中間
            e.Handled = true; // 防止事件繼續傳播

            // 延遲一點點時間確保事件處理完成
            Dispatcher.BeginInvoke(new Action(() =>
            {
                ShowFunctionMenu();
            }), DispatcherPriority.Background);
        }

        private void OnMainWindowKeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Escape)
            {
                System.Diagnostics.Debug.WriteLine("按下ESC鍵，關閉聊天記錄和工作記錄視窗");

                // 關閉訊息記錄視窗
                if (messageWindow != null && messageWindow.IsVisible)
                {
                    messageWindow.Hide();
                    System.Diagnostics.Debug.WriteLine("關閉訊息記錄視窗");
                }

                // 關閉待辦工作視窗
                if (todoWindow != null && todoWindow.IsVisible)
                {
                    todoWindow.Hide();
                    System.Diagnostics.Debug.WriteLine("關閉待辦工作視窗");
                }

                e.Handled = true;
            }
            // 添加強制關閉快捷鍵：Ctrl+Shift+Q
            else if (e.Key == Key.Q && (Keyboard.Modifiers & ModifierKeys.Control) == ModifierKeys.Control &&
                     (Keyboard.Modifiers & ModifierKeys.Shift) == ModifierKeys.Shift)
            {
                System.Diagnostics.Debug.WriteLine("按下 Ctrl+Shift+Q，強制關閉程式");

                // 直接使用最強力的關閉方式
                try
                {
                    System.Diagnostics.Process.GetCurrentProcess().Kill();
                }
                catch
                {
                    System.Environment.Exit(0);
                }

                e.Handled = true;
            }
        }

        #endregion

        private void ShowInputDialog()
        {
            // 建立輸入對話框
            var inputDialog = new InputDialog();

            // 等待對話框載入完成後設定位置
            inputDialog.Loaded += (s, e) =>
            {
                // 使用主視窗位置計算角色中心
                var characterCenterX = this.Left + this.Width / 2;
                var characterCenterY = this.Top + this.Height / 2;

                // 計算輸入框位置（角色中心）
                var dialogLeft = characterCenterX - inputDialog.ActualWidth / 2;
                var dialogTop = characterCenterY - inputDialog.ActualHeight / 2;

                // 檢查螢幕邊界
                var workingArea = SystemParameters.WorkArea;

                // 水平邊界檢查
                if (dialogLeft < workingArea.Left)
                    dialogLeft = workingArea.Left + 10;
                else if (dialogLeft + inputDialog.ActualWidth > workingArea.Right)
                    dialogLeft = workingArea.Right - inputDialog.ActualWidth - 10;

                // 垂直邊界檢查
                if (dialogTop < workingArea.Top)
                    dialogTop = workingArea.Top + 10;
                else if (dialogTop + inputDialog.ActualHeight > workingArea.Bottom)
                    dialogTop = workingArea.Bottom - inputDialog.ActualHeight - 10;

                inputDialog.Left = dialogLeft;
                inputDialog.Top = dialogTop;

                System.Diagnostics.Debug.WriteLine($"輸入框位置（角色中心）: 角色({characterCenterX}, {characterCenterY}) 輸入框({dialogLeft}, {dialogTop})");
            };

            // 顯示對話框
            bool? result = inputDialog.ShowDialog();

            System.Diagnostics.Debug.WriteLine($"ShowInputDialog: result={result}, IsConfirmed={inputDialog.IsConfirmed}, InputText='{inputDialog.InputText}'");

            if (result == true && inputDialog.IsConfirmed)
            {
                var userInput = inputDialog.InputText;
                System.Diagnostics.Debug.WriteLine($"ShowInputDialog: 處理用戶輸入='{userInput}'");

                if (!string.IsNullOrWhiteSpace(userInput))
                {
                    // 檢查是否以"工"開頭
                    if (userInput.StartsWith("工"))
                    {
                        // 工字開頭不記錄在聊天視窗與聊天記錄檔案
                        // 去掉"工"字，儲存至工作記錄檔案並顯示在工作記錄視窗
                        var taskContent = userInput.Substring(1).Trim();
                        if (!string.IsNullOrEmpty(taskContent))
                        {
                            // 儲存至工作記錄檔案
                            LogWorkTask(taskContent);
                            System.Diagnostics.Debug.WriteLine($"ShowInputDialog: 記錄工作任務='{taskContent}'");

                            // 顯示確認回覆
                            var workReply = GetWorkRecordReply();
                            ShowReplyWindow($"{workReply}\n已記錄：{taskContent}");

                            // 更新待辦工作視窗（如果開啟的話）
                            if (todoWindow.IsVisible)
                            {
                                todoWindow.RefreshTodos();
                            }
                        }
                    }
                    else
                    {
                        // 非工字開頭的輸入才記錄到對話記錄
                        LogChatMessage(userInput);
                        System.Diagnostics.Debug.WriteLine($"ShowInputDialog: 記錄輸入='{userInput}'");

                        if (IsCalculation(userInput))
                        {
                            // 數字計算，顯示計算結果（ProcessCalculation內部會記錄計算結果）
                            var calculationReply = ProcessCalculation(userInput);

                            // 顯示計算結果
                            ShowReplyWindow(calculationReply);
                            System.Diagnostics.Debug.WriteLine($"ShowInputDialog: 處理計算='{userInput}'");

                            // 更新訊息記錄視窗（如果開啟的話）
                            if (messageWindow.IsVisible)
                            {
                                messageWindow.RefreshMessages();
                                System.Diagnostics.Debug.WriteLine($"ShowInputDialog: 更新訊息記錄視窗完成");
                            }
                        }
                        else
                        {
                            // 一般對話，顯示隨機回覆
                            var reply = GetRandomReply();
                            ShowReplyWindow(reply);
                            System.Diagnostics.Debug.WriteLine($"ShowInputDialog: 顯示回覆視窗完成");

                            // 更新訊息記錄視窗（如果開啟的話）
                            if (messageWindow.IsVisible)
                            {
                                messageWindow.RefreshMessages();
                                System.Diagnostics.Debug.WriteLine($"ShowInputDialog: 更新訊息記錄視窗完成");
                            }
                        }
                    }
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"ShowInputDialog: 輸入未確認或取消");
            }
        }

        private bool IsTaskLikeInput(string input)
        {
            // 簡單判斷是否為任務類型的輸入
            var taskKeywords = new[] { "提醒", "記住", "待辦", "任務", "要做", "完成", "處理", "安排", "工" };
            return taskKeywords.Any(keyword => input.Contains(keyword));
        }

        private bool IsCalculation(string input)
        {
            // 檢查是否包含數字和運算符號
            var hasNumber = input.Any(char.IsDigit);
            var hasOperator = input.Any(c => "+-*/=".Contains(c));
            return hasNumber && hasOperator;
        }

        private string ProcessCalculation(string input)
        {
            try
            {
                // 移除空格和等號後的內容
                var expression = input.Split('=')[0].Trim().Replace(" ", "");

                // 簡單的數學表達式計算
                var result = EvaluateExpression(expression);

                // 記錄計算結果到檔案
                LogCalculationResult(expression, result);

                // 獲取計算回覆，並替換{result}
                var calculationReply = GetCalculationReply(result);

                return calculationReply;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"計算錯誤: {ex.Message}");
                return "« Je ne comprends pas ce calcul. »　我不懂這個計算。";
            }
        }

        private double EvaluateExpression(string expression)
        {
            // 簡單的數學表達式計算器
            // 支援 +, -, *, / 運算
            var dataTable = new System.Data.DataTable();
            var result = dataTable.Compute(expression, null);
            return Convert.ToDouble(result);
        }

        private string GetCalculationReply(double result)
        {
            if (dialogueCategories.ContainsKey("response_calculation") && dialogueCategories["response_calculation"].Count > 0)
            {
                var replies = dialogueCategories["response_calculation"];
                var random = new Random();
                var selectedReply = replies[random.Next(replies.Count)];

                // 替換{result}為實際計算結果
                return selectedReply.Replace("{result}", result.ToString());
            }
            return $"« Le résultat est {result}. »　結果是{result}。";
        }

        private Dictionary<string, List<string>> dialogueCategories = new Dictionary<string, List<string>>();

        private void LoadDialogueFile()
        {
            try
            {
                if (!File.Exists("Dialogue.txt"))
                {
                    System.Diagnostics.Debug.WriteLine("Dialogue.txt 檔案不存在");
                    return;
                }

                var lines = File.ReadAllLines("Dialogue.txt", System.Text.Encoding.UTF8);
                string currentCategory = "";

                foreach (var line in lines)
                {
                    var trimmedLine = line.Trim();

                    // 跳過空行和註解
                    if (string.IsNullOrEmpty(trimmedLine) || trimmedLine.StartsWith("#"))
                        continue;

                    // 檢查是否為類別標題
                    if (trimmedLine.StartsWith("[") && trimmedLine.EndsWith("]"))
                    {
                        currentCategory = trimmedLine.Substring(1, trimmedLine.Length - 2);
                        if (!dialogueCategories.ContainsKey(currentCategory))
                        {
                            dialogueCategories[currentCategory] = new List<string>();
                        }
                    }
                    else if (!string.IsNullOrEmpty(currentCategory))
                    {
                        // 添加對話內容到當前類別
                        dialogueCategories[currentCategory].Add(trimmedLine);
                    }
                }

                System.Diagnostics.Debug.WriteLine($"載入對話檔案完成，共 {dialogueCategories.Count} 個類別");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"載入對話檔案失敗: {ex.Message}");
            }
        }

        private string GetRandomReply()
        {
            // 如果對話類別為空，先載入檔案
            if (dialogueCategories.Count == 0)
            {
                LoadDialogueFile();
            }

            // 預設回覆由[random_normal]類別下隨機挑選
            if (dialogueCategories.ContainsKey("random_normal") && dialogueCategories["random_normal"].Count > 0)
            {
                var replies = dialogueCategories["random_normal"];
                var random = new Random();
                return replies[random.Next(replies.Count)];
            }

            // 如果沒有找到random_normal類別，返回預設回覆
            return "« Bonjour. »　你好。";
        }

        private string GetWorkRecordReply()
        {
            // 如果對話類別為空，先載入檔案
            if (dialogueCategories.Count == 0)
            {
                LoadDialogueFile();
            }

            // 使用工作記錄專用回覆
            if (dialogueCategories.ContainsKey("response_work_record") && dialogueCategories["response_work_record"].Count > 0)
            {
                var replies = dialogueCategories["response_work_record"];
                var random = new Random();
                return replies[random.Next(replies.Count)];
            }

            // 如果沒有找到工作記錄類別，返回預設回覆
            return "好的，我會處理。";
        }

        private string GetNewsReply()
        {
            // 如果對話類別為空，先載入檔案
            if (dialogueCategories.Count == 0)
            {
                LoadDialogueFile();
            }

            // 使用新聞回應專用回覆
            if (dialogueCategories.ContainsKey("response_news") && dialogueCategories["response_news"].Count > 0)
            {
                var replies = dialogueCategories["response_news"];
                var random = new Random();
                return replies[random.Next(replies.Count)];
            }

            // 如果沒有找到新聞回應類別，返回預設回覆
            return "看看今天有什麼新聞吧。";
        }

        public void ForceClose()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("ForceClose: 開始強制關閉程式");

                // 使用 Dispatcher 確保在 UI 線程執行
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    try
                    {
                        System.Diagnostics.Debug.WriteLine("ForceClose: 在UI線程中執行關閉");

                        // 停止所有計時器
                        if (animationTimer != null)
                        {
                            animationTimer.Stop();
                            animationTimer = null;
                        }
                        if (blinkTimer != null)
                        {
                            blinkTimer.Stop();
                            blinkTimer = null;
                        }
                        if (randomAnimationTimer != null)
                        {
                            randomAnimationTimer.Stop();
                            randomAnimationTimer = null;
                        }
                        if (autoReplyTimer != null)
                        {
                            autoReplyTimer.Stop();
                            autoReplyTimer = null;
                        }

                        // 關閉所有子視窗
                        if (messageWindow != null)
                        {
                            try { messageWindow.Close(); } catch { }
                            messageWindow = null;
                        }
                        if (todoWindow != null)
                        {
                            try { todoWindow.Close(); } catch { }
                            todoWindow = null;
                        }

                        // 設定關閉標記
                        this.Topmost = false;
                        this.WindowState = WindowState.Normal;

                        System.Diagnostics.Debug.WriteLine("ForceClose: 準備關閉主視窗");

                        // 延遲一點再關閉
                        var closeTimer = new System.Windows.Threading.DispatcherTimer();
                        closeTimer.Interval = TimeSpan.FromMilliseconds(100);
                        closeTimer.Tick += (s, e) =>
                        {
                            closeTimer.Stop();
                            try
                            {

                                Application.Current.Shutdown();
                                System.Environment.Exit(0);
                            }
                            catch
                            {
                                System.Environment.Exit(0);
                            }
                        };
                        closeTimer.Start();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"ForceClose UI線程錯誤: {ex.Message}");
                        System.Environment.Exit(0);
                    }
                }));

                // 備用關閉機制
                var backupTimer = new System.Threading.Timer(_ =>
                {
                    System.Diagnostics.Debug.WriteLine("ForceClose: 備用關閉機制啟動");
                    System.Environment.Exit(0);
                }, null, 1000, System.Threading.Timeout.Infinite);

            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ForceClose 發生錯誤: {ex.Message}");
                System.Environment.Exit(0);
            }
        }

        private void ShowFunctionMenu()
        {
            var functionMenu = new FunctionMenu(messageWindow, todoWindow, this);

            // 等待視窗載入完成後設定位置
            functionMenu.Loaded += (s, e) =>
            {
                // 使用主視窗位置計算角色中心（與輸入框位置一樣）
                var characterCenterX = this.Left + this.Width / 2;
                var characterCenterY = this.Top + this.Height / 2;

                // 計算選單位置（角色中心）
                double menuLeft = characterCenterX - functionMenu.ActualWidth / 2;
                double menuTop = characterCenterY - functionMenu.ActualHeight / 2;

                // 獲取螢幕工作區域
                var workingArea = SystemParameters.WorkArea;

                // 檢查左邊界
                if (menuLeft < workingArea.Left)
                {
                    menuLeft = workingArea.Left + 10; // 留10像素邊距
                }

                // 檢查右邊界
                if (menuLeft + functionMenu.ActualWidth > workingArea.Right)
                {
                    menuLeft = workingArea.Right - functionMenu.ActualWidth - 10; // 留10像素邊距
                }

                // 檢查上邊界
                if (menuTop < workingArea.Top)
                {
                    menuTop = workingArea.Top + 10; // 留10像素邊距
                }

                // 檢查下邊界
                if (menuTop + functionMenu.ActualHeight > workingArea.Bottom)
                {
                    menuTop = workingArea.Bottom - functionMenu.ActualHeight - 10; // 留10像素邊距
                }

                functionMenu.Left = menuLeft;
                functionMenu.Top = menuTop;

                System.Diagnostics.Debug.WriteLine($"功能選單位置: ({menuLeft}, {menuTop})");
                System.Diagnostics.Debug.WriteLine($"螢幕工作區域: {workingArea}");
            };

            functionMenu.Show();
        }
    }

    public class RssItem
    {
        public string Title { get; set; }
        public string Link { get; set; }
        public string Description { get; set; }
    }
}
