<Window x:Class="DesktopAssistantWidget.InputDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="輸入訊息" Height="60" Width="250"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        Topmost="True"
        ResizeMode="NoResize"
        ShowInTaskbar="False">

    <Border Background="#F0F8FF" BorderBrush="#4682B4" BorderThickness="2" CornerRadius="5" Opacity="0.85">
        <TextBox x:Name="InputTextBox"
                 FontSize="20"
                 Padding="8"
                 BorderThickness="0"
                 Background="Transparent"
                 AcceptsReturn="False"
                 TextWrapping="Wrap"
                 MaxLength="200"
                 Margin="5"/>
    </Border>
</Window>
