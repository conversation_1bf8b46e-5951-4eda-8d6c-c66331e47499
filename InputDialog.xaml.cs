using System;
using System.IO;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;

namespace DesktopAssistantWidget
{
    public partial class InputDialog : Window
    {
        private DispatcherTimer autoCloseTimer;
        public string InputText { get; private set; } = "";
        public bool IsConfirmed { get; private set; } = false;

        public InputDialog()
        {
            InitializeComponent();
            InitializeDialog();
        }

        private void InitializeDialog()
        {
            // 設定透明度為85%（15%透明）
            this.Opacity = 0.85;

            // 設定自動關閉計時器 (10秒)
            autoCloseTimer = new DispatcherTimer();
            autoCloseTimer.Interval = TimeSpan.FromSeconds(10);
            autoCloseTimer.Tick += AutoCloseTimer_Tick;
            
            // 初始不啟動計時器
            
            // 設定焦點到輸入框
            this.Loaded += (s, e) => 
            {
                InputTextBox.Focus();
                // 初始獲得焦點，不啟動計時器
            };

            // 設定鍵盤事件
            this.KeyDown += InputDialog_KeyDown;
            InputTextBox.KeyDown += InputTextBox_KeyDown;

            // 設定滑鼠右鍵事件
            this.MouseRightButtonDown += InputDialog_MouseRightButtonDown;
            
            // 添加視窗焦點事件
            this.Activated += InputDialog_Activated;
            this.Deactivated += InputDialog_Deactivated;
        }

        private void AutoCloseTimer_Tick(object sender, EventArgs e)
        {
            autoCloseTimer.Stop();
            this.DialogResult = false;
            this.Close();
        }

        private void InputDialog_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Escape)
            {
                autoCloseTimer.Stop();
                this.DialogResult = false;
                this.Close();
            }
        }

        private void InputTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                e.Handled = true;
                ConfirmInput();
            }
        }

        private void ConfirmInput()
        {
            autoCloseTimer.Stop();
            InputText = InputTextBox.Text.Trim();

            System.Diagnostics.Debug.WriteLine($"ConfirmInput: 輸入文字='{InputText}'");

            if (!string.IsNullOrEmpty(InputText))
            {
                IsConfirmed = true;
                this.DialogResult = true;
                System.Diagnostics.Debug.WriteLine($"ConfirmInput: 確認輸入，IsConfirmed={IsConfirmed}");
            }

            this.Close();
        }

        private void InputDialog_MouseRightButtonDown(object sender, MouseButtonEventArgs e)
        {
            // 右鍵點擊關閉對話框
            autoCloseTimer.Stop();
            this.DialogResult = false;
            this.Close();
            e.Handled = true;
        }

        private void InputDialog_Activated(object sender, EventArgs e)
        {
            // 視窗獲得焦點時停止計時器
            autoCloseTimer.Stop();
            System.Diagnostics.Debug.WriteLine("輸入對話框獲得焦點，停止自動關閉計時器");
        }

        private void InputDialog_Deactivated(object sender, EventArgs e)
        {
            // 視窗失去焦點時啟動計時器
            autoCloseTimer.Start();
            System.Diagnostics.Debug.WriteLine("輸入對話框失去焦點，啟動自動關閉計時器 (10秒)");
        }
    }
}

