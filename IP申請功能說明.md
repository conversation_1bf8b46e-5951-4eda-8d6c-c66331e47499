# 🌐 IP申請功能說明

## ✨ 功能概述

IP申請表單是桌面寵物的新增功能，提供完整的IP地址申請管理系統，包括表單填寫、Excel文件生成、工作記錄追蹤等功能。

## 🚀 如何使用

### 開啟表單
1. 右鍵點擊桌面寵物
2. 選擇 "🌐 IP申請" 選項
3. 表單視窗會在角色上方開啟

### 填寫表單
| 欄位 | 類型 | 必填 | 說明 |
|------|------|------|------|
| **單位** | 輸入框 | ✅ | 申請單位名稱 |
| **IP** | 輸入框 | ✅ | IP地址 |
| **MAC** | 輸入框 | ✅ | MAC地址 |
| **類型** | 下拉選單 | ✅ | 設備類型 |
| **申請人** | 輸入框 | ✅ | 申請人姓名 |
| **備註** | 輸入框 | ❌ | 備註信息 |

### 設備類型選項
- OA電腦
- NBT
- 筆記型電腦
- IOT設備
- 補褶機
- ATM

## 📋 表單驗證

### 必填欄位檢查
- 所有標記 `*` 的欄位都必須填寫
- 如有遺漏會顯示警告訊息
- 必須選擇設備類型

### 自動處理
- **備註欄位**：如果留空，自動填入當日日期 (yyyymmdd)
- **欄位清除**：提交後自動清除 IP、MAC、申請人欄位
- **單位保留**：方便同單位連續申請

## 📊 Excel文件生成

### 文件命名規則
```
ip申請-{單位}-{yyyymmdd}.xlsx
```
**示例**：`ip申請-資訊部-20251226.xlsx`

### Excel表格結構
| 欄位 | 說明 |
|------|------|
| 序號 | 自動編號 |
| 單位 | 申請單位 |
| IP | IP地址 |
| MAC | MAC地址 |
| 類型 | 設備類型 |
| 申請人 | 申請人姓名 |
| 備註 | 備註信息 |
| 申請時間 | 自動記錄時間戳 |

### 文件處理邏輯
- **新申請**：創建新Excel文件
- **同單位同日期**：追加到現有文件
- **自動格式化**：標題加粗、居中對齊、自動調整列寬

## 💼 工作記錄整合

### 記錄格式
```
[HH:MM:SS] IP開通 {單位} {IP} ({類型})
```

### 記錄示例
```
[14:30:15] IP開通 資訊部 ************* (OA電腦)
[14:35:20] IP開通 財務部 ************* (筆記型電腦)
[15:00:10] IP開通 總務部 ************* (IOT設備)
```

### 記錄位置
- 自動添加到當日工作記錄文件
- 可在工作記錄視窗中查看
- 支援勾選完成功能

## 🔄 連續申請流程

### 使用場景
同一個單位需要申請多個IP地址時，可以使用連續申請功能。

### 操作步驟
1. **第一次申請**：填寫所有欄位並提交
2. **自動保留**：單位欄位內容保留
3. **繼續填寫**：只需填寫 IP、MAC、類型、申請人
4. **重複提交**：所有記錄會追加到同一個Excel文件

### 優勢
- 減少重複輸入
- 統一管理同單位申請
- 提高工作效率

## 🎛️ 表單操作

### 按鈕功能
- **送出**：提交表單並生成記錄
- **清除全部**：清空所有欄位
- **關閉**：關閉表單視窗

### 快捷操作
- 提交成功後會顯示確認訊息
- 自動清除指定欄位準備下次輸入
- 支援鍵盤操作（Tab切換欄位）

## 📁 文件管理

### 生成的文件
```
WindowsWidget01/
├── ip申請-資訊部-20251226.xlsx    # Excel申請記錄
├── ip申請-財務部-20251226.xlsx    # 不同單位的記錄
└── chat_logs/
    └── 工作記錄-2025-12-26.txt    # 工作記錄
```

### 文件特點
- **Excel文件**：完整的申請記錄，可用於存檔
- **工作記錄**：簡潔的操作記錄，便於追蹤
- **自動備份**：每日獨立文件，便於管理

## 🛠️ 技術特性

### 依賴庫
- **openpyxl**：Excel文件處理
- **tkinter.ttk**：下拉選單組件

### 安全特性
- 輸入驗證防止空值
- 文件操作異常處理
- 自動格式化防止錯誤

### 性能優化
- 智能文件檢測
- 增量數據追加
- 內存友好的處理方式

## ⚠️ 注意事項

### 使用建議
1. **IP格式**：建議使用標準IP格式 (如: *************)
2. **MAC格式**：建議使用標準MAC格式 (如: 00:11:22:33:44:55)
3. **單位名稱**：保持一致性，避免重複創建文件
4. **定期備份**：重要的Excel文件建議定期備份

### 故障排除
1. **無法打開表單**：檢查是否安裝 openpyxl
2. **Excel無法生成**：檢查文件權限和磁盤空間
3. **工作記錄未添加**：檢查 chat_logs 目錄權限

## 🎯 使用範例

### 範例1：單次申請
```
單位: 資訊部
IP: *************
MAC: 00:11:22:33:44:55
類型: OA電腦
申請人: 張三
備註: 新進員工設備
```

### 範例2：連續申請
```
第一筆：
單位: 財務部
IP: *************
類型: 筆記型電腦
申請人: 李四

第二筆：(單位自動保留)
IP: *************
類型: OA電腦
申請人: 王五
```

## 🎉 功能優勢

- **🚀 高效率**：快速填寫和提交
- **📊 標準化**：統一的Excel格式
- **🔄 連續性**：支援批量申請
- **📝 可追蹤**：完整的記錄系統
- **🛡️ 可靠性**：完善的驗證機制
- **💾 可存檔**：標準Excel格式便於存檔

現在你的桌面寵物具備了完整的IP申請管理功能！🎊
