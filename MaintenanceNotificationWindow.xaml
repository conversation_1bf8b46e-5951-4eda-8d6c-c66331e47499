<Window x:Class="DesktopAssistantWidget.MaintenanceNotificationWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="🔧 維護通告表單" Height="600" Width="450"
        WindowStyle="SingleBorderWindow"
        ResizeMode="NoResize"
        Topmost="True"
        ShowInTaskbar="False"
        Background="#F0F8FF">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 表單內容 -->
        <ScrollViewer Grid.Row="0" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- 類型 -->
                <Grid Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="類型 *" VerticalAlignment="Center" FontWeight="Bold"/>
                    <ComboBox x:Name="TypeComboBox" Grid.Column="1" Height="30" FontSize="14" SelectedIndex="1">
                        <ComboBoxItem Content="緊急"/>
                        <ComboBoxItem Content="計劃性"/>
                    </ComboBox>
                </Grid>

                <!-- 日期 -->
                <Grid Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="日期 *" VerticalAlignment="Center" FontWeight="Bold"/>
                    <DatePicker x:Name="DatePicker" Grid.Column="1" Height="30" FontSize="14"
                                SelectedDateFormat="Short" FirstDayOfWeek="Monday"/>
                </Grid>
                
                <!-- 通知單位 -->
                <Grid Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="通知單位 *" VerticalAlignment="Center" FontWeight="Bold"/>
                    <TextBox x:Name="NotifyUnitTextBox" Grid.Column="1" Height="30" Padding="5" FontSize="14"/>
                </Grid>
                
                <!-- 施工單位 -->
                <Grid Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="施工單位 *" VerticalAlignment="Center" FontWeight="Bold"/>
                    <TextBox x:Name="ConstructionUnitTextBox" Grid.Column="1" Height="30" Padding="5" FontSize="14"/>
                </Grid>
                
                <!-- 影響單位 -->
                <Grid Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="影響單位 *" VerticalAlignment="Center" FontWeight="Bold"/>
                    <TextBox x:Name="AffectedUnitTextBox" Grid.Column="1" Height="30" Padding="5" FontSize="14"/>
                </Grid>
                
                <!-- 施工時間 -->
                <Grid Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="施工時間 *" VerticalAlignment="Center" FontWeight="Bold"/>
                    <ComboBox x:Name="StartTimeComboBox" Grid.Column="1" Height="30" FontSize="14"/>
                </Grid>

                <!-- 施工結束時間 -->
                <Grid Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="結束時間 *" VerticalAlignment="Center" FontWeight="Bold"/>
                    <ComboBox x:Name="EndTimeComboBox" Grid.Column="1" Height="30" FontSize="14"/>
                </Grid>
                
                <!-- 影響項目 -->
                <Grid Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="影響項目 *" VerticalAlignment="Center" FontWeight="Bold"/>
                    <TextBox x:Name="AffectedItemsTextBox" Grid.Column="1" Height="30" Padding="5" FontSize="14"/>
                </Grid>
                
                <!-- 事由 -->
                <Grid Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="事由 *" VerticalAlignment="Top" FontWeight="Bold" Margin="0,5,0,0"/>
                    <TextBox x:Name="ReasonTextBox" Grid.Column="1" Height="60" Padding="5" FontSize="14" 
                             AcceptsReturn="True" TextWrapping="Wrap" VerticalScrollBarVisibility="Auto"/>
                </Grid>
                
                <!-- 產出信件勾選項 -->
                <Grid Margin="0,15,0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="產出信件:" VerticalAlignment="Center" FontWeight="Bold"/>
                    <CheckBox x:Name="GenerateEmailCheckBox" Grid.Column="1" Content="自動產生Outlook信件" 
                              IsChecked="True" FontSize="14" VerticalAlignment="Center"/>
                </Grid>
                
                <!-- 說明文字 -->
                <TextBlock Text="* 為必填欄位" FontSize="12" Foreground="Red" Margin="0,10,0,0"/>
                <TextBlock Text="日期：點選日曆或輸入mmdd格式 (如：1225)" FontSize="12" Foreground="Gray" Margin="0,5,0,0"/>
                <TextBlock Text="時間：30分鐘間隔選項，30小時制（00:00-30:00）" FontSize="12" Foreground="Gray" Margin="0,2,0,0"/>
            </StackPanel>
        </ScrollViewer>
        
        <!-- 按鈕區域 -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="SubmitButton" Content="送出" Width="80" Height="35" Margin="5" 
                    Background="#4CAF50" Foreground="White" FontWeight="Bold" Click="SubmitButton_Click"/>
            <Button x:Name="ClearButton" Content="清除全部" Width="80" Height="35" Margin="5" 
                    Background="#FF9800" Foreground="White" FontWeight="Bold" Click="ClearButton_Click"/>
            <Button x:Name="CloseButton" Content="關閉" Width="80" Height="35" Margin="5" 
                    Background="#F44336" Foreground="White" FontWeight="Bold" Click="CloseButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
