using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using OfficeOpenXml;
using OfficeOpenXml.Style;

namespace DesktopAssistantWidget
{
    public partial class IpApplicationWindow : Window
    {
        private MainWindow mainWindow;

        public IpApplicationWindow(MainWindow parent)
        {
            InitializeComponent();
            mainWindow = parent;
            InitializeWindow();
        }

        private void InitializeWindow()
        {
            // 設定EPPlus授權
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            
            // 設定視窗位置
            this.WindowStartupLocation = WindowStartupLocation.Manual;
            
            // 設定焦點到第一個輸入框
            this.Loaded += (s, e) => UnitTextBox.Focus();
        }

        private void SubmitButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateForm())
            {
                try
                {
                    // 處理備註欄位
                    if (string.IsNullOrWhiteSpace(RemarksTextBox.Text))
                    {
                        RemarksTextBox.Text = DateTime.Now.ToString("yyyyMMdd");
                    }

                    // 創建申請記錄
                    var application = new IpApplication
                    {
                        Unit = UnitTextBox.Text.Trim(),
                        IP = IpTextBox.Text.Trim(),
                        MAC = MacTextBox.Text.Trim(),
                        Type = ((ComboBoxItem)TypeComboBox.SelectedItem).Content.ToString(),
                        Applicant = ApplicantTextBox.Text.Trim(),
                        Remarks = RemarksTextBox.Text.Trim(),
                        ApplicationTime = DateTime.Now
                    };

                    // 生成Excel文件
                    GenerateExcelFile(application);

                    // 添加到工作記錄
                    AddToWorkRecord(application);

                    // 顯示成功訊息
                    MessageBox.Show("IP申請提交成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);

                    // 清除指定欄位
                    ClearSpecificFields();

                    System.Diagnostics.Debug.WriteLine($"IP申請提交成功: {application.Unit} - {application.IP}");
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"提交失敗：{ex.Message}", "錯誤", MessageBoxButton.OK, MessageBoxImage.Error);
                    System.Diagnostics.Debug.WriteLine($"IP申請提交失敗: {ex.Message}");
                }
            }
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(UnitTextBox.Text))
            {
                MessageBox.Show("請填寫單位", "驗證失敗", MessageBoxButton.OK, MessageBoxImage.Warning);
                UnitTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(IpTextBox.Text))
            {
                MessageBox.Show("請填寫IP地址", "驗證失敗", MessageBoxButton.OK, MessageBoxImage.Warning);
                IpTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(MacTextBox.Text))
            {
                MessageBox.Show("請填寫MAC地址", "驗證失敗", MessageBoxButton.OK, MessageBoxImage.Warning);
                MacTextBox.Focus();
                return false;
            }

            if (TypeComboBox.SelectedItem == null)
            {
                MessageBox.Show("請選擇設備類型", "驗證失敗", MessageBoxButton.OK, MessageBoxImage.Warning);
                TypeComboBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(ApplicantTextBox.Text))
            {
                MessageBox.Show("請填寫申請人", "驗證失敗", MessageBoxButton.OK, MessageBoxImage.Warning);
                ApplicantTextBox.Focus();
                return false;
            }

            return true;
        }

        private void GenerateExcelFile(IpApplication application)
        {
            var fileName = $"{DateTime.Now:yyyyMMdd}申請單.xls";
            var filePath = Path.Combine(Environment.CurrentDirectory, fileName);

            using (var package = new ExcelPackage())
            {
                ExcelWorksheet worksheet;

                // 檢查文件是否已存在
                if (File.Exists(filePath))
                {
                    // 載入現有文件並追加數據
                    using (var existingPackage = new ExcelPackage(new FileInfo(filePath)))
                    {
                        worksheet = existingPackage.Workbook.Worksheets[0];
                        var nextRow = worksheet.Dimension?.End.Row + 1 ?? 2;

                        // 添加新記錄到現有文件
                        AddNewRecord(worksheet, nextRow, application);

                        // 保存文件
                        existingPackage.Save();
                    }
                    return;
                }
                else
                {
                    // 創建新文件，按照範例格式
                    worksheet = package.Workbook.Worksheets.Add("分配表匯入");

                    // 設定標題行
                    SetupWorksheetHeaders(worksheet);

                    // 添加第一筆記錄
                    AddNewRecord(worksheet, 2, application);
                }

                // 保存文件
                package.SaveAs(new FileInfo(filePath));
            }

            System.Diagnostics.Debug.WriteLine($"Excel文件已生成: {fileName}");
        }

        private void SetupWorksheetHeaders(ExcelWorksheet worksheet)
        {
            // 設定標題行（第1行）
            worksheet.Cells[1, 1].Value = "ip";
            worksheet.Cells[1, 2].Value = "mac";
            worksheet.Cells[1, 3].Value = "單位";
            worksheet.Cells[1, 4].Value = "使用者";
            worksheet.Cells[1, 5].Value = "白名單描述";
            worksheet.Cells[1, 6].Value = "分配表匯入";
            worksheet.Cells[1, 7].Value = "ip授權";
            worksheet.Cells[1, 8].Value = "ip綁定";
            worksheet.Cells[1, 9].Value = "mac綁定";
            worksheet.Cells[1, 10].Value = "ip綁定（by偵測器)";
            worksheet.Cells[1, 11].Value = "自動mac綁定";
            worksheet.Cells[1, 12].Value = "作業系統例外";

            // 格式化標題
            using (var range = worksheet.Cells[1, 1, 1, 12])
            {
                range.Style.Font.Bold = true;
                range.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            }
        }

        private void AddNewRecord(ExcelWorksheet worksheet, int row, IpApplication application)
        {
            // 填入表單數據
            worksheet.Cells[row, 1].Value = application.IP;           // ip
            worksheet.Cells[row, 2].Value = application.MAC;          // mac
            worksheet.Cells[row, 3].Value = application.Unit;         // 單位
            worksheet.Cells[row, 4].Value = application.Applicant;    // 使用者
            worksheet.Cells[row, 5].Value = application.Type;         // 白名單描述（類別）
            worksheet.Cells[row, 6].Value = $"{DateTime.Now:yyyyMMdd}申請單"; // 分配表匯入

            // 固定欄位（按照範例表格）
            worksheet.Cells[row, 7].Value = "啟用";   // ip授權
            worksheet.Cells[row, 8].Value = "啟用";   // ip綁定
            worksheet.Cells[row, 9].Value = "啟用";   // mac綁定
            worksheet.Cells[row, 10].Value = "停用";  // ip綁定（by偵測器)
            worksheet.Cells[row, 11].Value = "啟用";  // 自動mac綁定
            worksheet.Cells[row, 12].Value = "停用";  // 作業系統例外

            // 自動調整列寬
            worksheet.Cells.AutoFitColumns();
        }

        private void AddToWorkRecord(IpApplication application)
        {
            try
            {
                var timestamp = DateTime.Now.ToString("HH:mm:ss");
                var workRecord = $"IP開通 {application.Unit} {application.IP} ({application.Type})";
                
                // 調用MainWindow的LogWorkTask方法
                if (mainWindow != null)
                {
                    mainWindow.LogWorkTaskFromExternal(workRecord);
                }
                
                System.Diagnostics.Debug.WriteLine($"工作記錄已添加: {workRecord}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"添加工作記錄失敗: {ex.Message}");
            }
        }

        private void ClearSpecificFields()
        {
            // 清除IP、MAC、申請人欄位，保留單位
            IpTextBox.Clear();
            MacTextBox.Clear();
            ApplicantTextBox.Clear();
            TypeComboBox.SelectedIndex = 0; // 重新設定為OA電腦
            RemarksTextBox.Clear();

            // 設定焦點到IP欄位
            IpTextBox.Focus();
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            // 清除所有欄位
            UnitTextBox.Clear();
            IpTextBox.Clear();
            MacTextBox.Clear();
            ApplicantTextBox.Clear();
            TypeComboBox.SelectedIndex = 0; // 設定為OA電腦
            RemarksTextBox.Clear();

            // 設定焦點到第一個欄位
            UnitTextBox.Focus();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    // IP申請記錄類別
    public class IpApplication
    {
        public string Unit { get; set; }
        public string IP { get; set; }
        public string MAC { get; set; }
        public string Type { get; set; }
        public string Applicant { get; set; }
        public string Remarks { get; set; }
        public DateTime ApplicationTime { get; set; }
    }
}
