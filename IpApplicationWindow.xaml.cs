using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;

namespace DesktopAssistantWidget
{
    public partial class IpApplicationWindow : Window
    {
        private MainWindow mainWindow;

        public IpApplicationWindow(MainWindow parent)
        {
            InitializeComponent();
            mainWindow = parent;
            InitializeWindow();
        }

        private void InitializeWindow()
        {
            // NPOI不需要授權設定
            
            // 設定視窗位置
            this.WindowStartupLocation = WindowStartupLocation.Manual;
            
            // 設定焦點到第一個輸入框
            this.Loaded += (s, e) => UnitTextBox.Focus();
        }

        private void SubmitButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateForm())
            {
                try
                {
                    // 處理備註欄位
                    if (string.IsNullOrWhiteSpace(RemarksTextBox.Text))
                    {
                        RemarksTextBox.Text = DateTime.Now.ToString("yyyyMMdd");
                    }

                    // 創建申請記錄
                    var application = new IpApplication
                    {
                        Unit = UnitTextBox.Text.Trim(),
                        IP = IpTextBox.Text.Trim(),
                        MAC = MacTextBox.Text.Trim(),
                        Type = ((ComboBoxItem)TypeComboBox.SelectedItem).Content.ToString(),
                        Applicant = ApplicantTextBox.Text.Trim(),
                        Remarks = RemarksTextBox.Text.Trim(),
                        ApplicationTime = DateTime.Now
                    };

                    // 生成Excel文件
                    GenerateExcelFile(application);

                    // 添加到工作記錄
                    AddToWorkRecord(application);

                    // 顯示成功訊息
                    MessageBox.Show("IP申請提交成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);

                    // 清除指定欄位
                    ClearSpecificFields();

                    System.Diagnostics.Debug.WriteLine($"IP申請提交成功: {application.Unit} - {application.IP}");
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"提交失敗：{ex.Message}", "錯誤", MessageBoxButton.OK, MessageBoxImage.Error);
                    System.Diagnostics.Debug.WriteLine($"IP申請提交失敗: {ex.Message}");
                }
            }
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(UnitTextBox.Text))
            {
                MessageBox.Show("請填寫單位", "驗證失敗", MessageBoxButton.OK, MessageBoxImage.Warning);
                UnitTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(IpTextBox.Text))
            {
                MessageBox.Show("請填寫IP地址", "驗證失敗", MessageBoxButton.OK, MessageBoxImage.Warning);
                IpTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(MacTextBox.Text))
            {
                MessageBox.Show("請填寫MAC地址", "驗證失敗", MessageBoxButton.OK, MessageBoxImage.Warning);
                MacTextBox.Focus();
                return false;
            }

            if (TypeComboBox.SelectedItem == null)
            {
                MessageBox.Show("請選擇設備類型", "驗證失敗", MessageBoxButton.OK, MessageBoxImage.Warning);
                TypeComboBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(ApplicantTextBox.Text))
            {
                MessageBox.Show("請填寫申請人", "驗證失敗", MessageBoxButton.OK, MessageBoxImage.Warning);
                ApplicantTextBox.Focus();
                return false;
            }

            return true;
        }

        private void GenerateExcelFile(IpApplication application)
        {
            var fileName = $"ip申請-{application.Unit}-{DateTime.Now:yyyyMMdd}.xls";
            var filePath = Path.Combine(Environment.CurrentDirectory, fileName);

            IWorkbook workbook;
            ISheet worksheet;

            // 檢查文件是否已存在
            if (File.Exists(filePath))
            {
                // 載入現有文件
                using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                {
                    workbook = new HSSFWorkbook(fileStream);
                }
                worksheet = workbook.GetSheetAt(0);
                var nextRow = worksheet.LastRowNum + 1;

                // 添加新記錄
                AddNewRecord(worksheet, nextRow, application);
            }
            else
            {
                // 創建新文件
                workbook = new HSSFWorkbook();
                worksheet = workbook.CreateSheet("分配表匯入");

                // 設定標題行
                SetupWorksheetHeaders(worksheet);

                // 添加第一筆記錄
                AddNewRecord(worksheet, 1, application);
            }

            // 保存文件
            using (var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write))
            {
                workbook.Write(fileStream);
            }

            System.Diagnostics.Debug.WriteLine($"Excel文件已生成: {fileName}");
        }

        private void SetupWorksheetHeaders(ISheet worksheet)
        {
            // 創建標題行（第0行，NPOI使用0基索引）
            var headerRow = worksheet.CreateRow(0);

            headerRow.CreateCell(0).SetCellValue("ip");
            headerRow.CreateCell(1).SetCellValue("mac");
            headerRow.CreateCell(2).SetCellValue("單位");
            headerRow.CreateCell(3).SetCellValue("使用者");
            headerRow.CreateCell(4).SetCellValue("白名單描述");
            headerRow.CreateCell(5).SetCellValue("分配表匯入");
            headerRow.CreateCell(6).SetCellValue("ip授權");
            headerRow.CreateCell(7).SetCellValue("ip綁定");
            headerRow.CreateCell(8).SetCellValue("mac綁定");
            headerRow.CreateCell(9).SetCellValue("ip綁定（by偵測器)");
            headerRow.CreateCell(10).SetCellValue("自動mac綁定");
            headerRow.CreateCell(11).SetCellValue("作業系統例外");

            // 設定標題行樣式（粗體）
            var workbook = worksheet.Workbook;
            var boldStyle = workbook.CreateCellStyle();
            var boldFont = workbook.CreateFont();
            boldFont.IsBold = true;
            boldStyle.SetFont(boldFont);
            boldStyle.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;

            for (int i = 0; i < 12; i++)
            {
                headerRow.GetCell(i).CellStyle = boldStyle;
            }
        }

        private void AddNewRecord(ISheet worksheet, int row, IpApplication application)
        {
            // 創建新行（NPOI使用0基索引）
            var dataRow = worksheet.CreateRow(row);

            // 填入表單數據
            dataRow.CreateCell(0).SetCellValue(application.IP);           // ip
            dataRow.CreateCell(1).SetCellValue(application.MAC);          // mac
            dataRow.CreateCell(2).SetCellValue(application.Unit);         // 單位
            dataRow.CreateCell(3).SetCellValue(application.Applicant);    // 使用者
            dataRow.CreateCell(4).SetCellValue(application.Type);         // 白名單描述（類別）
            dataRow.CreateCell(5).SetCellValue($"{DateTime.Now:yyyyMMdd}申請單"); // 分配表匯入

            // 固定欄位（按照範例表格）
            dataRow.CreateCell(6).SetCellValue("啟用");   // ip授權
            dataRow.CreateCell(7).SetCellValue("啟用");   // ip綁定
            dataRow.CreateCell(8).SetCellValue("啟用");   // mac綁定
            dataRow.CreateCell(9).SetCellValue("停用");   // ip綁定（by偵測器)
            dataRow.CreateCell(10).SetCellValue("啟用");  // 自動mac綁定
            dataRow.CreateCell(11).SetCellValue("停用");  // 作業系統例外

            // 自動調整列寬
            for (int i = 0; i < 12; i++)
            {
                worksheet.AutoSizeColumn(i);
            }
        }

        private void AddToWorkRecord(IpApplication application)
        {
            try
            {
                var timestamp = DateTime.Now.ToString("HH:mm:ss");
                var workRecord = $"IP開通 {application.Unit} {application.IP} ({application.Type})";
                
                // 調用MainWindow的LogWorkTask方法
                if (mainWindow != null)
                {
                    mainWindow.LogWorkTaskFromExternal(workRecord);
                }
                
                System.Diagnostics.Debug.WriteLine($"工作記錄已添加: {workRecord}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"添加工作記錄失敗: {ex.Message}");
            }
        }

        private void ClearSpecificFields()
        {
            // 清除IP、MAC、申請人欄位，保留單位
            IpTextBox.Clear();
            MacTextBox.Clear();
            ApplicantTextBox.Clear();
            TypeComboBox.SelectedIndex = 0; // 重新設定為OA電腦
            RemarksTextBox.Clear();

            // 設定焦點到IP欄位
            IpTextBox.Focus();
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            // 清除所有欄位
            UnitTextBox.Clear();
            IpTextBox.Clear();
            MacTextBox.Clear();
            ApplicantTextBox.Clear();
            TypeComboBox.SelectedIndex = 0; // 設定為OA電腦
            RemarksTextBox.Clear();

            // 設定焦點到第一個欄位
            UnitTextBox.Focus();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    // IP申請記錄類別
    public class IpApplication
    {
        public string Unit { get; set; }
        public string IP { get; set; }
        public string MAC { get; set; }
        public string Type { get; set; }
        public string Applicant { get; set; }
        public string Remarks { get; set; }
        public DateTime ApplicationTime { get; set; }
    }
}
